{"ast": null, "code": "// Google OAuth Configuration for Houzer API\n// Using custom OAuth URL provided by <PERSON><PERSON><PERSON> backend\nexport const GOOGLE_OAUTH_CONFIG={// Houzer Google OAuth URL\nauthUrl:process.env.REACT_APP_GOOGLE_AUTH_URL||\"https://betaapi.houzer.co.in/houzer/oauth2/authorize/google?redirect_uri=https://beta.app.houzer.co.in/houzer/oauth2/redirect\",// OAuth scopes - what information we want to access\nscope:\"profile email\",// Response type\nresponseType:\"code\",// Access type\naccessType:\"offline\",// Prompt\nprompt:\"consent\"};// Environment setup instructions:\n// Create a .env file in your project root and add:\n// REACT_APP_GOOGLE_AUTH_URL=https://betaapi.houzer.co.in/houzer/oauth2/authorize/google?redirect_uri=https://beta.app.houzer.co.in/houzer/oauth2/redirect\nexport default GOOGLE_OAUTH_CONFIG;", "map": {"version": 3, "names": ["GOOGLE_OAUTH_CONFIG", "authUrl", "process", "env", "REACT_APP_GOOGLE_AUTH_URL", "scope", "responseType", "accessType", "prompt"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/src/config/googleOAuth.js"], "sourcesContent": ["// Google OAuth Configuration for Houzer API\n// Using custom OAuth URL provided by <PERSON><PERSON><PERSON> backend\n\nexport const GOOGLE_OAUTH_CONFIG = {\n  // Houzer Google OAuth URL\n  authUrl: process.env.REACT_APP_GOOGLE_AUTH_URL || \"https://betaapi.houzer.co.in/houzer/oauth2/authorize/google?redirect_uri=https://beta.app.houzer.co.in/houzer/oauth2/redirect\",\n\n  // OAuth scopes - what information we want to access\n  scope: \"profile email\",\n\n  // Response type\n  responseType: \"code\",\n\n  // Access type\n  accessType: \"offline\",\n\n  // Prompt\n  prompt: \"consent\"\n};\n\n// Environment setup instructions:\n// Create a .env file in your project root and add:\n// REACT_APP_GOOGLE_AUTH_URL=https://betaapi.houzer.co.in/houzer/oauth2/authorize/google?redirect_uri=https://beta.app.houzer.co.in/houzer/oauth2/redirect\n\nexport default GOOGLE_OAUTH_CONFIG;\n"], "mappings": "AAAA;AACA;AAEA,MAAO,MAAM,CAAAA,mBAAmB,CAAG,CACjC;AACAC,OAAO,CAAEC,OAAO,CAACC,GAAG,CAACC,yBAAyB,EAAI,+HAA+H,CAEjL;AACAC,KAAK,CAAE,eAAe,CAEtB;AACAC,YAAY,CAAE,MAAM,CAEpB;AACAC,UAAU,CAAE,SAAS,CAErB;AACAC,MAAM,CAAE,SACV,CAAC,CAED;AACA;AACA;AAEA,cAAe,CAAAR,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}