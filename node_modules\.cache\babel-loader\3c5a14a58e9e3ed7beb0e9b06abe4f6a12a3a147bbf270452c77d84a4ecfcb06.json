{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport PropTypes from 'prop-types';\nimport deepmerge from '@mui/utils/deepmerge';\nimport merge from './merge';\n\n// The breakpoint **start** at this value.\n// For instance with the first breakpoint xs: [xs, sm[.\nexport const values = {\n  xs: 0,\n  // phone\n  sm: 600,\n  // tablet\n  md: 900,\n  // small laptop\n  lg: 1200,\n  // desktop\n  xl: 1536 // large screen\n};\nconst defaultBreakpoints = {\n  // Sorted ASC by size. That's important.\n  // It can't be configured as it's used statically for propTypes.\n  keys: ['xs', 'sm', 'md', 'lg', 'xl'],\n  up: key => \"@media (min-width:\".concat(values[key], \"px)\")\n};\nexport function handleBreakpoints(props, propValue, styleFromPropValue) {\n  const theme = props.theme || {};\n  if (Array.isArray(propValue)) {\n    const themeBreakpoints = theme.breakpoints || defaultBreakpoints;\n    return propValue.reduce((acc, item, index) => {\n      acc[themeBreakpoints.up(themeBreakpoints.keys[index])] = styleFromPropValue(propValue[index]);\n      return acc;\n    }, {});\n  }\n  if (typeof propValue === 'object') {\n    const themeBreakpoints = theme.breakpoints || defaultBreakpoints;\n    return Object.keys(propValue).reduce((acc, breakpoint) => {\n      // key is breakpoint\n      if (Object.keys(themeBreakpoints.values || values).indexOf(breakpoint) !== -1) {\n        const mediaKey = themeBreakpoints.up(breakpoint);\n        acc[mediaKey] = styleFromPropValue(propValue[breakpoint], breakpoint);\n      } else {\n        const cssKey = breakpoint;\n        acc[cssKey] = propValue[cssKey];\n      }\n      return acc;\n    }, {});\n  }\n  const output = styleFromPropValue(propValue);\n  return output;\n}\nfunction breakpoints(styleFunction) {\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const newStyleFunction = props => {\n    const theme = props.theme || {};\n    const base = styleFunction(props);\n    const themeBreakpoints = theme.breakpoints || defaultBreakpoints;\n    const extended = themeBreakpoints.keys.reduce((acc, key) => {\n      if (props[key]) {\n        acc = acc || {};\n        acc[themeBreakpoints.up(key)] = styleFunction(_extends({\n          theme\n        }, props[key]));\n      }\n      return acc;\n    }, null);\n    return merge(base, extended);\n  };\n  newStyleFunction.propTypes = process.env.NODE_ENV !== 'production' ? _extends({}, styleFunction.propTypes, {\n    xs: PropTypes.object,\n    sm: PropTypes.object,\n    md: PropTypes.object,\n    lg: PropTypes.object,\n    xl: PropTypes.object\n  }) : {};\n  newStyleFunction.filterProps = ['xs', 'sm', 'md', 'lg', 'xl', ...styleFunction.filterProps];\n  return newStyleFunction;\n}\nexport function createEmptyBreakpointObject() {\n  let breakpointsInput = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _breakpointsInput$key;\n  const breakpointsInOrder = (_breakpointsInput$key = breakpointsInput.keys) == null ? void 0 : _breakpointsInput$key.reduce((acc, key) => {\n    const breakpointStyleKey = breakpointsInput.up(key);\n    acc[breakpointStyleKey] = {};\n    return acc;\n  }, {});\n  return breakpointsInOrder || {};\n}\nexport function removeUnusedBreakpoints(breakpointKeys, style) {\n  return breakpointKeys.reduce((acc, key) => {\n    const breakpointOutput = acc[key];\n    const isBreakpointUnused = !breakpointOutput || Object.keys(breakpointOutput).length === 0;\n    if (isBreakpointUnused) {\n      delete acc[key];\n    }\n    return acc;\n  }, style);\n}\nexport function mergeBreakpointsInOrder(breakpointsInput) {\n  const emptyBreakpoints = createEmptyBreakpointObject(breakpointsInput);\n  for (var _len = arguments.length, styles = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    styles[_key - 1] = arguments[_key];\n  }\n  const mergedOutput = [emptyBreakpoints, ...styles].reduce((prev, next) => deepmerge(prev, next), {});\n  return removeUnusedBreakpoints(Object.keys(emptyBreakpoints), mergedOutput);\n}\n\n// compute base for responsive values; e.g.,\n// [1,2,3] => {xs: true, sm: true, md: true}\n// {xs: 1, sm: 2, md: 3} => {xs: true, sm: true, md: true}\nexport function computeBreakpointsBase(breakpointValues, themeBreakpoints) {\n  // fixed value\n  if (typeof breakpointValues !== 'object') {\n    return {};\n  }\n  const base = {};\n  const breakpointsKeys = Object.keys(themeBreakpoints);\n  if (Array.isArray(breakpointValues)) {\n    breakpointsKeys.forEach((breakpoint, i) => {\n      if (i < breakpointValues.length) {\n        base[breakpoint] = true;\n      }\n    });\n  } else {\n    breakpointsKeys.forEach(breakpoint => {\n      if (breakpointValues[breakpoint] != null) {\n        base[breakpoint] = true;\n      }\n    });\n  }\n  return base;\n}\nexport function resolveBreakpointValues(_ref) {\n  let {\n    values: breakpointValues,\n    breakpoints: themeBreakpoints,\n    base: customBase\n  } = _ref;\n  const base = customBase || computeBreakpointsBase(breakpointValues, themeBreakpoints);\n  const keys = Object.keys(base);\n  if (keys.length === 0) {\n    return breakpointValues;\n  }\n  let previous;\n  return keys.reduce((acc, breakpoint, i) => {\n    if (Array.isArray(breakpointValues)) {\n      acc[breakpoint] = breakpointValues[i] != null ? breakpointValues[i] : breakpointValues[previous];\n      previous = i;\n    } else if (typeof breakpointValues === 'object') {\n      acc[breakpoint] = breakpointValues[breakpoint] != null ? breakpointValues[breakpoint] : breakpointValues[previous];\n      previous = breakpoint;\n    } else {\n      acc[breakpoint] = breakpointValues;\n    }\n    return acc;\n  }, {});\n}\nexport default breakpoints;", "map": {"version": 3, "names": ["_extends", "PropTypes", "deepmerge", "merge", "values", "xs", "sm", "md", "lg", "xl", "defaultBreakpoints", "keys", "up", "key", "concat", "handleBreakpoints", "props", "propValue", "styleFromPropValue", "theme", "Array", "isArray", "themeBreakpoints", "breakpoints", "reduce", "acc", "item", "index", "Object", "breakpoint", "indexOf", "mediaKey", "cssKey", "output", "styleFunction", "newStyleFunction", "base", "extended", "propTypes", "process", "env", "NODE_ENV", "object", "filterProps", "createEmptyBreakpointObject", "breakpointsInput", "arguments", "length", "undefined", "_breakpointsInput$key", "breakpointsInOrder", "breakpointStyle<PERSON>ey", "removeUnusedBreakpoints", "breakpoint<PERSON><PERSON><PERSON>", "style", "breakpointOutput", "isBreakpointUnused", "mergeBreakpointsInOrder", "emptyBreakpoints", "_len", "styles", "_key", "mergedOutput", "prev", "next", "computeBreakpointsBase", "breakpoint<PERSON><PERSON><PERSON>", "breakpointsKeys", "for<PERSON>ach", "i", "resolveBreakpointValues", "_ref", "customBase", "previous"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/system/esm/breakpoints.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport PropTypes from 'prop-types';\nimport deepmerge from '@mui/utils/deepmerge';\nimport merge from './merge';\n\n// The breakpoint **start** at this value.\n// For instance with the first breakpoint xs: [xs, sm[.\nexport const values = {\n  xs: 0,\n  // phone\n  sm: 600,\n  // tablet\n  md: 900,\n  // small laptop\n  lg: 1200,\n  // desktop\n  xl: 1536 // large screen\n};\nconst defaultBreakpoints = {\n  // Sorted ASC by size. That's important.\n  // It can't be configured as it's used statically for propTypes.\n  keys: ['xs', 'sm', 'md', 'lg', 'xl'],\n  up: key => `@media (min-width:${values[key]}px)`\n};\nexport function handleBreakpoints(props, propValue, styleFromPropValue) {\n  const theme = props.theme || {};\n  if (Array.isArray(propValue)) {\n    const themeBreakpoints = theme.breakpoints || defaultBreakpoints;\n    return propValue.reduce((acc, item, index) => {\n      acc[themeBreakpoints.up(themeBreakpoints.keys[index])] = styleFromPropValue(propValue[index]);\n      return acc;\n    }, {});\n  }\n  if (typeof propValue === 'object') {\n    const themeBreakpoints = theme.breakpoints || defaultBreakpoints;\n    return Object.keys(propValue).reduce((acc, breakpoint) => {\n      // key is breakpoint\n      if (Object.keys(themeBreakpoints.values || values).indexOf(breakpoint) !== -1) {\n        const mediaKey = themeBreakpoints.up(breakpoint);\n        acc[mediaKey] = styleFromPropValue(propValue[breakpoint], breakpoint);\n      } else {\n        const cssKey = breakpoint;\n        acc[cssKey] = propValue[cssKey];\n      }\n      return acc;\n    }, {});\n  }\n  const output = styleFromPropValue(propValue);\n  return output;\n}\nfunction breakpoints(styleFunction) {\n  // false positive\n  // eslint-disable-next-line react/function-component-definition\n  const newStyleFunction = props => {\n    const theme = props.theme || {};\n    const base = styleFunction(props);\n    const themeBreakpoints = theme.breakpoints || defaultBreakpoints;\n    const extended = themeBreakpoints.keys.reduce((acc, key) => {\n      if (props[key]) {\n        acc = acc || {};\n        acc[themeBreakpoints.up(key)] = styleFunction(_extends({\n          theme\n        }, props[key]));\n      }\n      return acc;\n    }, null);\n    return merge(base, extended);\n  };\n  newStyleFunction.propTypes = process.env.NODE_ENV !== 'production' ? _extends({}, styleFunction.propTypes, {\n    xs: PropTypes.object,\n    sm: PropTypes.object,\n    md: PropTypes.object,\n    lg: PropTypes.object,\n    xl: PropTypes.object\n  }) : {};\n  newStyleFunction.filterProps = ['xs', 'sm', 'md', 'lg', 'xl', ...styleFunction.filterProps];\n  return newStyleFunction;\n}\nexport function createEmptyBreakpointObject(breakpointsInput = {}) {\n  var _breakpointsInput$key;\n  const breakpointsInOrder = (_breakpointsInput$key = breakpointsInput.keys) == null ? void 0 : _breakpointsInput$key.reduce((acc, key) => {\n    const breakpointStyleKey = breakpointsInput.up(key);\n    acc[breakpointStyleKey] = {};\n    return acc;\n  }, {});\n  return breakpointsInOrder || {};\n}\nexport function removeUnusedBreakpoints(breakpointKeys, style) {\n  return breakpointKeys.reduce((acc, key) => {\n    const breakpointOutput = acc[key];\n    const isBreakpointUnused = !breakpointOutput || Object.keys(breakpointOutput).length === 0;\n    if (isBreakpointUnused) {\n      delete acc[key];\n    }\n    return acc;\n  }, style);\n}\nexport function mergeBreakpointsInOrder(breakpointsInput, ...styles) {\n  const emptyBreakpoints = createEmptyBreakpointObject(breakpointsInput);\n  const mergedOutput = [emptyBreakpoints, ...styles].reduce((prev, next) => deepmerge(prev, next), {});\n  return removeUnusedBreakpoints(Object.keys(emptyBreakpoints), mergedOutput);\n}\n\n// compute base for responsive values; e.g.,\n// [1,2,3] => {xs: true, sm: true, md: true}\n// {xs: 1, sm: 2, md: 3} => {xs: true, sm: true, md: true}\nexport function computeBreakpointsBase(breakpointValues, themeBreakpoints) {\n  // fixed value\n  if (typeof breakpointValues !== 'object') {\n    return {};\n  }\n  const base = {};\n  const breakpointsKeys = Object.keys(themeBreakpoints);\n  if (Array.isArray(breakpointValues)) {\n    breakpointsKeys.forEach((breakpoint, i) => {\n      if (i < breakpointValues.length) {\n        base[breakpoint] = true;\n      }\n    });\n  } else {\n    breakpointsKeys.forEach(breakpoint => {\n      if (breakpointValues[breakpoint] != null) {\n        base[breakpoint] = true;\n      }\n    });\n  }\n  return base;\n}\nexport function resolveBreakpointValues({\n  values: breakpointValues,\n  breakpoints: themeBreakpoints,\n  base: customBase\n}) {\n  const base = customBase || computeBreakpointsBase(breakpointValues, themeBreakpoints);\n  const keys = Object.keys(base);\n  if (keys.length === 0) {\n    return breakpointValues;\n  }\n  let previous;\n  return keys.reduce((acc, breakpoint, i) => {\n    if (Array.isArray(breakpointValues)) {\n      acc[breakpoint] = breakpointValues[i] != null ? breakpointValues[i] : breakpointValues[previous];\n      previous = i;\n    } else if (typeof breakpointValues === 'object') {\n      acc[breakpoint] = breakpointValues[breakpoint] != null ? breakpointValues[breakpoint] : breakpointValues[previous];\n      previous = breakpoint;\n    } else {\n      acc[breakpoint] = breakpointValues;\n    }\n    return acc;\n  }, {});\n}\nexport default breakpoints;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,KAAK,MAAM,SAAS;;AAE3B;AACA;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,EAAE,EAAE,CAAC;EACL;EACAC,EAAE,EAAE,GAAG;EACP;EACAC,EAAE,EAAE,GAAG;EACP;EACAC,EAAE,EAAE,IAAI;EACR;EACAC,EAAE,EAAE,IAAI,CAAC;AACX,CAAC;AACD,MAAMC,kBAAkB,GAAG;EACzB;EACA;EACAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACpCC,EAAE,EAAEC,GAAG,yBAAAC,MAAA,CAAyBV,MAAM,CAACS,GAAG,CAAC;AAC7C,CAAC;AACD,OAAO,SAASE,iBAAiBA,CAACC,KAAK,EAAEC,SAAS,EAAEC,kBAAkB,EAAE;EACtE,MAAMC,KAAK,GAAGH,KAAK,CAACG,KAAK,IAAI,CAAC,CAAC;EAC/B,IAAIC,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,EAAE;IAC5B,MAAMK,gBAAgB,GAAGH,KAAK,CAACI,WAAW,IAAIb,kBAAkB;IAChE,OAAOO,SAAS,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,EAAEC,KAAK,KAAK;MAC5CF,GAAG,CAACH,gBAAgB,CAACV,EAAE,CAACU,gBAAgB,CAACX,IAAI,CAACgB,KAAK,CAAC,CAAC,CAAC,GAAGT,kBAAkB,CAACD,SAAS,CAACU,KAAK,CAAC,CAAC;MAC7F,OAAOF,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR;EACA,IAAI,OAAOR,SAAS,KAAK,QAAQ,EAAE;IACjC,MAAMK,gBAAgB,GAAGH,KAAK,CAACI,WAAW,IAAIb,kBAAkB;IAChE,OAAOkB,MAAM,CAACjB,IAAI,CAACM,SAAS,CAAC,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEI,UAAU,KAAK;MACxD;MACA,IAAID,MAAM,CAACjB,IAAI,CAACW,gBAAgB,CAAClB,MAAM,IAAIA,MAAM,CAAC,CAAC0B,OAAO,CAACD,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;QAC7E,MAAME,QAAQ,GAAGT,gBAAgB,CAACV,EAAE,CAACiB,UAAU,CAAC;QAChDJ,GAAG,CAACM,QAAQ,CAAC,GAAGb,kBAAkB,CAACD,SAAS,CAACY,UAAU,CAAC,EAAEA,UAAU,CAAC;MACvE,CAAC,MAAM;QACL,MAAMG,MAAM,GAAGH,UAAU;QACzBJ,GAAG,CAACO,MAAM,CAAC,GAAGf,SAAS,CAACe,MAAM,CAAC;MACjC;MACA,OAAOP,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR;EACA,MAAMQ,MAAM,GAAGf,kBAAkB,CAACD,SAAS,CAAC;EAC5C,OAAOgB,MAAM;AACf;AACA,SAASV,WAAWA,CAACW,aAAa,EAAE;EAClC;EACA;EACA,MAAMC,gBAAgB,GAAGnB,KAAK,IAAI;IAChC,MAAMG,KAAK,GAAGH,KAAK,CAACG,KAAK,IAAI,CAAC,CAAC;IAC/B,MAAMiB,IAAI,GAAGF,aAAa,CAAClB,KAAK,CAAC;IACjC,MAAMM,gBAAgB,GAAGH,KAAK,CAACI,WAAW,IAAIb,kBAAkB;IAChE,MAAM2B,QAAQ,GAAGf,gBAAgB,CAACX,IAAI,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEZ,GAAG,KAAK;MAC1D,IAAIG,KAAK,CAACH,GAAG,CAAC,EAAE;QACdY,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;QACfA,GAAG,CAACH,gBAAgB,CAACV,EAAE,CAACC,GAAG,CAAC,CAAC,GAAGqB,aAAa,CAAClC,QAAQ,CAAC;UACrDmB;QACF,CAAC,EAAEH,KAAK,CAACH,GAAG,CAAC,CAAC,CAAC;MACjB;MACA,OAAOY,GAAG;IACZ,CAAC,EAAE,IAAI,CAAC;IACR,OAAOtB,KAAK,CAACiC,IAAI,EAAEC,QAAQ,CAAC;EAC9B,CAAC;EACDF,gBAAgB,CAACG,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzC,QAAQ,CAAC,CAAC,CAAC,EAAEkC,aAAa,CAACI,SAAS,EAAE;IACzGjC,EAAE,EAAEJ,SAAS,CAACyC,MAAM;IACpBpC,EAAE,EAAEL,SAAS,CAACyC,MAAM;IACpBnC,EAAE,EAAEN,SAAS,CAACyC,MAAM;IACpBlC,EAAE,EAAEP,SAAS,CAACyC,MAAM;IACpBjC,EAAE,EAAER,SAAS,CAACyC;EAChB,CAAC,CAAC,GAAG,CAAC,CAAC;EACPP,gBAAgB,CAACQ,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAGT,aAAa,CAACS,WAAW,CAAC;EAC3F,OAAOR,gBAAgB;AACzB;AACA,OAAO,SAASS,2BAA2BA,CAAA,EAAwB;EAAA,IAAvBC,gBAAgB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC/D,IAAIG,qBAAqB;EACzB,MAAMC,kBAAkB,GAAG,CAACD,qBAAqB,GAAGJ,gBAAgB,CAAClC,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsC,qBAAqB,CAACzB,MAAM,CAAC,CAACC,GAAG,EAAEZ,GAAG,KAAK;IACvI,MAAMsC,kBAAkB,GAAGN,gBAAgB,CAACjC,EAAE,CAACC,GAAG,CAAC;IACnDY,GAAG,CAAC0B,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAC5B,OAAO1B,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOyB,kBAAkB,IAAI,CAAC,CAAC;AACjC;AACA,OAAO,SAASE,uBAAuBA,CAACC,cAAc,EAAEC,KAAK,EAAE;EAC7D,OAAOD,cAAc,CAAC7B,MAAM,CAAC,CAACC,GAAG,EAAEZ,GAAG,KAAK;IACzC,MAAM0C,gBAAgB,GAAG9B,GAAG,CAACZ,GAAG,CAAC;IACjC,MAAM2C,kBAAkB,GAAG,CAACD,gBAAgB,IAAI3B,MAAM,CAACjB,IAAI,CAAC4C,gBAAgB,CAAC,CAACR,MAAM,KAAK,CAAC;IAC1F,IAAIS,kBAAkB,EAAE;MACtB,OAAO/B,GAAG,CAACZ,GAAG,CAAC;IACjB;IACA,OAAOY,GAAG;EACZ,CAAC,EAAE6B,KAAK,CAAC;AACX;AACA,OAAO,SAASG,uBAAuBA,CAACZ,gBAAgB,EAAa;EACnE,MAAMa,gBAAgB,GAAGd,2BAA2B,CAACC,gBAAgB,CAAC;EAAC,SAAAc,IAAA,GAAAb,SAAA,CAAAC,MAAA,EADZa,MAAM,OAAAxC,KAAA,CAAAuC,IAAA,OAAAA,IAAA,WAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;IAAND,MAAM,CAAAC,IAAA,QAAAf,SAAA,CAAAe,IAAA;EAAA;EAEjE,MAAMC,YAAY,GAAG,CAACJ,gBAAgB,EAAE,GAAGE,MAAM,CAAC,CAACpC,MAAM,CAAC,CAACuC,IAAI,EAAEC,IAAI,KAAK9D,SAAS,CAAC6D,IAAI,EAAEC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;EACpG,OAAOZ,uBAAuB,CAACxB,MAAM,CAACjB,IAAI,CAAC+C,gBAAgB,CAAC,EAAEI,YAAY,CAAC;AAC7E;;AAEA;AACA;AACA;AACA,OAAO,SAASG,sBAAsBA,CAACC,gBAAgB,EAAE5C,gBAAgB,EAAE;EACzE;EACA,IAAI,OAAO4C,gBAAgB,KAAK,QAAQ,EAAE;IACxC,OAAO,CAAC,CAAC;EACX;EACA,MAAM9B,IAAI,GAAG,CAAC,CAAC;EACf,MAAM+B,eAAe,GAAGvC,MAAM,CAACjB,IAAI,CAACW,gBAAgB,CAAC;EACrD,IAAIF,KAAK,CAACC,OAAO,CAAC6C,gBAAgB,CAAC,EAAE;IACnCC,eAAe,CAACC,OAAO,CAAC,CAACvC,UAAU,EAAEwC,CAAC,KAAK;MACzC,IAAIA,CAAC,GAAGH,gBAAgB,CAACnB,MAAM,EAAE;QAC/BX,IAAI,CAACP,UAAU,CAAC,GAAG,IAAI;MACzB;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACLsC,eAAe,CAACC,OAAO,CAACvC,UAAU,IAAI;MACpC,IAAIqC,gBAAgB,CAACrC,UAAU,CAAC,IAAI,IAAI,EAAE;QACxCO,IAAI,CAACP,UAAU,CAAC,GAAG,IAAI;MACzB;IACF,CAAC,CAAC;EACJ;EACA,OAAOO,IAAI;AACb;AACA,OAAO,SAASkC,uBAAuBA,CAAAC,IAAA,EAIpC;EAAA,IAJqC;IACtCnE,MAAM,EAAE8D,gBAAgB;IACxB3C,WAAW,EAAED,gBAAgB;IAC7Bc,IAAI,EAAEoC;EACR,CAAC,GAAAD,IAAA;EACC,MAAMnC,IAAI,GAAGoC,UAAU,IAAIP,sBAAsB,CAACC,gBAAgB,EAAE5C,gBAAgB,CAAC;EACrF,MAAMX,IAAI,GAAGiB,MAAM,CAACjB,IAAI,CAACyB,IAAI,CAAC;EAC9B,IAAIzB,IAAI,CAACoC,MAAM,KAAK,CAAC,EAAE;IACrB,OAAOmB,gBAAgB;EACzB;EACA,IAAIO,QAAQ;EACZ,OAAO9D,IAAI,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEI,UAAU,EAAEwC,CAAC,KAAK;IACzC,IAAIjD,KAAK,CAACC,OAAO,CAAC6C,gBAAgB,CAAC,EAAE;MACnCzC,GAAG,CAACI,UAAU,CAAC,GAAGqC,gBAAgB,CAACG,CAAC,CAAC,IAAI,IAAI,GAAGH,gBAAgB,CAACG,CAAC,CAAC,GAAGH,gBAAgB,CAACO,QAAQ,CAAC;MAChGA,QAAQ,GAAGJ,CAAC;IACd,CAAC,MAAM,IAAI,OAAOH,gBAAgB,KAAK,QAAQ,EAAE;MAC/CzC,GAAG,CAACI,UAAU,CAAC,GAAGqC,gBAAgB,CAACrC,UAAU,CAAC,IAAI,IAAI,GAAGqC,gBAAgB,CAACrC,UAAU,CAAC,GAAGqC,gBAAgB,CAACO,QAAQ,CAAC;MAClHA,QAAQ,GAAG5C,UAAU;IACvB,CAAC,MAAM;MACLJ,GAAG,CAACI,UAAU,CAAC,GAAGqC,gBAAgB;IACpC;IACA,OAAOzC,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}