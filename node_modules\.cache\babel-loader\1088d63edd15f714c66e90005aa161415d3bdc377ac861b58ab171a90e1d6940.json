{"ast": null, "code": "import { get as t, set as e } from \"react-hook-form\";\nconst s = (e, s, o) => {\n    if (e && \"reportValidity\" in e) {\n      const r = t(o, s);\n      e.setCustomValidity(r && r.message || \"\"), e.reportValidity();\n    }\n  },\n  o = (t, e) => {\n    for (const o in e.fields) {\n      const r = e.fields[o];\n      r && r.ref && \"reportValidity\" in r.ref ? s(r.ref, o, t) : r.refs && r.refs.forEach(e => s(e, o, t));\n    }\n  },\n  r = (s, r) => {\n    r.shouldUseNativeValidation && o(s, r);\n    const f = {};\n    for (const o in s) {\n      const n = t(r.fields, o),\n        a = Object.assign(s[o] || {}, {\n          ref: n && n.ref\n        });\n      if (i(r.names || Object.keys(s), o)) {\n        const s = Object.assign({}, t(f, o));\n        e(s, \"root\", a), e(f, o, s);\n      } else e(f, o, a);\n    }\n    return f;\n  },\n  i = (t, e) => t.some(t => t.startsWith(e + \".\"));\nexport { r as toNestErrors, o as validateFieldsNatively };", "map": {"version": 3, "names": ["s", "e", "o", "r", "t", "setCustomValidity", "message", "reportValidity", "fields", "ref", "refs", "for<PERSON>ach", "shouldUseNativeValidation", "f", "n", "a", "Object", "assign", "i", "names", "keys", "some", "startsWith", "toNestErrors", "validateFieldsNatively"], "sources": ["C:\\Users\\<USER>\\Documents\\Project\\sample-authentication\\node_modules\\@hookform\\resolvers\\src\\validateFieldsNatively.ts", "C:\\Users\\<USER>\\Documents\\Project\\sample-authentication\\node_modules\\@hookform\\resolvers\\src\\toNestErrors.ts"], "sourcesContent": ["import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Ref,\n  ResolverOptions,\n  get,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  Field,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n  ResolverOptions,\n  get,\n  set,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => names.some((n) => n.startsWith(name + '.'));\n"], "mappings": ";AASA,MAAMA,CAAA,GAAoBA,CACxBC,CAAA,EACAD,CAAA,EACAE,CAAA;IAEA,IAAID,CAAA,IAAO,oBAAoBA,CAAA,EAAK;MAClC,MAAME,CAAA,GAAQC,CAAA,CAAIF,CAAA,EAAQF,CAAA;MAC1BC,CAAA,CAAII,iBAAA,CAAmBF,CAAA,IAASA,CAAA,CAAMG,OAAA,IAAY,KAElDL,CAAA,CAAIM,cAAA,EACN;IAAA;EAAA;EAIWL,CAAA,GAAyBA,CACpCE,CAAA,EACAH,CAAA;IAEA,KAAK,MAAMC,CAAA,IAAaD,CAAA,CAAQO,MAAA,EAAQ;MACtC,MAAML,CAAA,GAAQF,CAAA,CAAQO,MAAA,CAAON,CAAA;MACzBC,CAAA,IAASA,CAAA,CAAMM,GAAA,IAAO,oBAAoBN,CAAA,CAAMM,GAAA,GAClDT,CAAA,CAAkBG,CAAA,CAAMM,GAAA,EAAKP,CAAA,EAAWE,CAAA,IAC/BD,CAAA,CAAMO,IAAA,IACfP,CAAA,CAAMO,IAAA,CAAKC,OAAA,CAASV,CAAA,IAClBD,CAAA,CAAkBC,CAAA,EAAKC,CAAA,EAAWE,CAAA,EAGxC;IAAA;EAAA;ECzBWD,CAAA,GAAeA,CAC1BH,CAAA,EACAG,CAAA;IAEAA,CAAA,CAAQS,yBAAA,IAA6BV,CAAA,CAAuBF,CAAA,EAAQG,CAAA;IAEpE,MAAMU,CAAA,GAAc;IACpB,KAAK,MAAMX,CAAA,IAAQF,CAAA,EAAQ;MACzB,MAAMc,CAAA,GAAQV,CAAA,CAAID,CAAA,CAAQK,MAAA,EAAQN,CAAA;QAC5Ba,CAAA,GAAQC,MAAA,CAAOC,MAAA,CAAOjB,CAAA,CAAOE,CAAA,KAAS,IAAI;UAC9CO,GAAA,EAAKK,CAAA,IAASA,CAAA,CAAML;QAAA;MAGtB,IAAIS,CAAA,CAAmBf,CAAA,CAAQgB,KAAA,IAASH,MAAA,CAAOI,IAAA,CAAKpB,CAAA,GAASE,CAAA,GAAO;QAClE,MAAMF,CAAA,GAAmBgB,MAAA,CAAOC,MAAA,CAAO,IAAIb,CAAA,CAAIS,CAAA,EAAaX,CAAA;QAE5DD,CAAA,CAAID,CAAA,EAAkB,QAAQe,CAAA,GAC9Bd,CAAA,CAAIY,CAAA,EAAaX,CAAA,EAAMF,CAAA,CACzB;MAAA,OACEC,CAAA,CAAIY,CAAA,EAAaX,CAAA,EAAMa,CAAA,CAE3B;IAAA;IAEA,OAAOF,CAAA;EAAA;EAGHK,CAAA,GAAqBA,CACzBd,CAAA,EACAH,CAAA,KACGG,CAAA,CAAMiB,IAAA,CAAMjB,CAAA,IAAMA,CAAA,CAAEkB,UAAA,CAAWrB,CAAA,GAAO;AAAA,SAAAE,CAAA,IAAAoB,YAAA,EAAArB,CAAA,IAAAsB,sBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}