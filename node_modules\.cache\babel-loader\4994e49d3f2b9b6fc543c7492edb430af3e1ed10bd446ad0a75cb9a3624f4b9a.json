{"ast": null, "code": "'use client';\n\nexport { default } from './Container';\nexport { default as containerClasses } from './containerClasses';\nexport * from './containerClasses';", "map": {"version": 3, "names": ["default", "containerClasses"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/system/esm/Container/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Container';\nexport { default as containerClasses } from './containerClasses';\nexport * from './containerClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,oBAAoB;AAChE,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}