import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, mockAuthResponses, validFormData, invalidFormData } from '../utils/testUtils';
import Signup from '../../pages/Signup';
import authService from '../../api/authService';

// Mock the authService
jest.mock('../../api/authService');
const mockedAuthService = authService;

// Mock the navigate function
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock the components
jest.mock('../../components/GoogleOAuthButton', () => {
  return function MockGoogleOAuthButton({ onSuccess, onError, disabled, text }) {
    return (
      <button
        data-testid="google-oauth-button"
        disabled={disabled}
        onClick={() => onSuccess({ code: 'mock-google-code' })}
      >
        {text}
      </button>
    );
  };
});

jest.mock('../../components/OrDivider', () => {
  return function MockOrDivider() {
    return <div data-testid="or-divider">OR</div>;
  };
});

jest.mock('../../components/FormControlWrapper', () => {
  return function MockFormControlWrapper({ name, label, type, placeholder, error, helperText, control }) {
    const { Controller } = require('react-hook-form');
    return (
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <div>
            <label htmlFor={name}>{label}</label>
            <input
              {...field}
              id={name}
              type={type}
              placeholder={placeholder}
              data-testid={`input-${name}`}
            />
            {error && <div data-testid={`error-${name}`}>{helperText}</div>}
          </div>
        )}
      />
    );
  };
});

jest.mock('../../components/PasswordField', () => {
  return function MockPasswordField({ name, label, placeholder, error, helperText, control }) {
    const { Controller } = require('react-hook-form');
    return (
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <div>
            <label htmlFor={name}>{label}</label>
            <input
              {...field}
              id={name}
              type="password"
              placeholder={placeholder}
              data-testid={`input-${name}`}
            />
            {error && <div data-testid={`error-${name}`}>{helperText}</div>}
          </div>
        )}
      />
    );
  };
});

describe('Signup Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  describe('Rendering', () => {
    it('should render all form elements', () => {
      renderWithProviders(<Signup />);

      expect(screen.getByText('Create account')).toBeInTheDocument();
      expect(screen.getByText('Sign up to get started with your account')).toBeInTheDocument();
      expect(screen.getByTestId('google-oauth-button')).toBeInTheDocument();
      expect(screen.getByTestId('or-divider')).toBeInTheDocument();
      expect(screen.getByTestId('input-name')).toBeInTheDocument();
      expect(screen.getByTestId('input-email')).toBeInTheDocument();
      expect(screen.getByTestId('input-password')).toBeInTheDocument();
      expect(screen.getByTestId('input-confirmPassword')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
    });

    it('should render navigation links', () => {
      renderWithProviders(<Signup />);

      expect(screen.getByText('Already have an account?')).toBeInTheDocument();
      expect(screen.getByText('Sign in')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show validation errors for empty fields', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Signup />);

      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-name')).toHaveTextContent('Name is required');
        expect(screen.getByTestId('error-email')).toHaveTextContent('Email is required');
        expect(screen.getByTestId('error-password')).toHaveTextContent('Password is required');
        expect(screen.getByTestId('error-confirmPassword')).toHaveTextContent('Confirm password is required');
      });
    });

    it('should show validation error for invalid email', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Signup />);

      const emailInput = screen.getByTestId('input-email');
      await user.type(emailInput, invalidFormData.signup.email);

      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-email')).toHaveTextContent('Invalid email address');
      });
    });

    it('should show validation error for password mismatch', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Signup />);

      const passwordInput = screen.getByTestId('input-password');
      const confirmPasswordInput = screen.getByTestId('input-confirmPassword');

      await user.type(passwordInput, 'password123');
      await user.type(confirmPasswordInput, 'differentpassword');

      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-confirmPassword')).toHaveTextContent('Passwords must match');
      });
    });

    it('should show validation error for weak password', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Signup />);

      const passwordInput = screen.getByTestId('input-password');
      await user.type(passwordInput, '123');

      const submitButton = screen.getByRole('button', { name: /create account/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-password')).toHaveTextContent('Password must be at least 8 characters');
      });
    });

    it('should not show validation errors for valid input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Signup />);

      const nameInput = screen.getByTestId('input-name');
      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const confirmPasswordInput = screen.getByTestId('input-confirmPassword');

      await user.type(nameInput, validFormData.signup.name);
      await user.type(emailInput, validFormData.signup.email);
      await user.type(passwordInput, validFormData.signup.password);
      await user.type(confirmPasswordInput, validFormData.signup.confirmPassword);

      await waitFor(() => {
        expect(screen.queryByTestId('error-name')).not.toBeInTheDocument();
        expect(screen.queryByTestId('error-email')).not.toBeInTheDocument();
        expect(screen.queryByTestId('error-password')).not.toBeInTheDocument();
        expect(screen.queryByTestId('error-confirmPassword')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Submission', () => {
    it('should submit form with valid data and navigate on success', async () => {
      const user = userEvent.setup();

      // Mock successful signup - call the success callback
      mockedAuthService.signup.mockImplementation(async (userData, errorCallback, successCallback) => {
        successCallback(mockAuthResponses.signupSuccess.data);
        return mockAuthResponses.signupSuccess.data;
      });

      renderWithProviders(<Signup />);

      const nameInput = screen.getByTestId('input-name');
      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const confirmPasswordInput = screen.getByTestId('input-confirmPassword');
      const submitButton = screen.getByRole('button', { name: /create account/i });

      await user.type(nameInput, validFormData.signup.name);
      await user.type(emailInput, validFormData.signup.email);
      await user.type(passwordInput, validFormData.signup.password);
      await user.type(confirmPasswordInput, validFormData.signup.confirmPassword);
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockedAuthService.signup).toHaveBeenCalledWith(
          {
            name: validFormData.signup.name,
            email: validFormData.signup.email,
            password: validFormData.signup.password,
          },
          expect.any(Function),
          expect.any(Function)
        );
      });

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/login');
      });
    });

    it('should show loading state during submission', async () => {
      const user = userEvent.setup();
      mockedAuthService.signup.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      renderWithProviders(<Signup />);

      const nameInput = screen.getByTestId('input-name');
      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const confirmPasswordInput = screen.getByTestId('input-confirmPassword');
      const submitButton = screen.getByRole('button', { name: /create account/i });

      await user.type(nameInput, validFormData.signup.name);
      await user.type(emailInput, validFormData.signup.email);
      await user.type(passwordInput, validFormData.signup.password);
      await user.type(confirmPasswordInput, validFormData.signup.confirmPassword);
      await user.click(submitButton);

      expect(screen.getByText('Creating account...')).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
    });

    it('should handle signup error and show error message', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Email already exists';

      // Mock failed signup - call the error callback
      mockedAuthService.signup.mockImplementation(async (userData, errorCallback, successCallback) => {
        errorCallback(errorMessage);
        throw errorMessage;
      });

      renderWithProviders(<Signup />);

      const nameInput = screen.getByTestId('input-name');
      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const confirmPasswordInput = screen.getByTestId('input-confirmPassword');
      const submitButton = screen.getByRole('button', { name: /create account/i });

      await user.type(nameInput, validFormData.signup.name);
      await user.type(emailInput, validFormData.signup.email);
      await user.type(passwordInput, validFormData.signup.password);
      await user.type(confirmPasswordInput, validFormData.signup.confirmPassword);
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('Google OAuth', () => {
    it('should handle Google OAuth success and navigate to profile', async () => {
      const user = userEvent.setup();

      // Mock successful Google login - call the success callback
      mockedAuthService.googleLogin.mockImplementation(async (googleData, errorCallback, successCallback) => {
        successCallback(mockAuthResponses.loginSuccess.data);
        return mockAuthResponses.loginSuccess.data;
      });

      renderWithProviders(<Signup />);

      const googleButton = screen.getByTestId('google-oauth-button');
      await user.click(googleButton);

      await waitFor(() => {
        expect(mockedAuthService.googleLogin).toHaveBeenCalledWith(
          { code: 'mock-google-code' },
          expect.any(Function),
          expect.any(Function)
        );
      });

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/profile');
      });
    });

    it('should handle Google OAuth error', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Google authentication failed';

      // Mock failed Google login - call the error callback
      mockedAuthService.googleLogin.mockImplementation(async (googleData, errorCallback, successCallback) => {
        errorCallback(errorMessage);
        throw errorMessage;
      });

      renderWithProviders(<Signup />);

      const googleButton = screen.getByTestId('google-oauth-button');
      await user.click(googleButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });
});
