{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"implementation\", \"lgDown\", \"lgUp\", \"mdDown\", \"mdUp\", \"smDown\", \"smUp\", \"xlDown\", \"xlUp\", \"xsDown\", \"xsUp\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport HiddenJs from './HiddenJs';\nimport HiddenCss from './HiddenCss';\n\n/**\n * Responsively hides children based on the selected implementation.\n *\n * @deprecated The Hidden component was deprecated in Material UI v5. To learn more, see [the Hidden section](/material-ui/migration/v5-component-changes/#hidden) of the migration docs.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction Hidden(props) {\n  const {\n      implementation = 'js',\n      lgDown = false,\n      lgUp = false,\n      mdDown = false,\n      mdUp = false,\n      smDown = false,\n      smUp = false,\n      xlDown = false,\n      xlUp = false,\n      xsDown = false,\n      xsUp = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  if (implementation === 'js') {\n    return /*#__PURE__*/_jsx(HiddenJs, _extends({\n      lgDown: lgDown,\n      lgUp: lgUp,\n      mdDown: mdDown,\n      mdUp: mdUp,\n      smDown: smDown,\n      smUp: smUp,\n      xlDown: xlDown,\n      xlUp: xlUp,\n      xsDown: xsDown,\n      xsUp: xsUp\n    }, other));\n  }\n  return /*#__PURE__*/_jsx(HiddenCss, _extends({\n    lgDown: lgDown,\n    lgUp: lgUp,\n    mdDown: mdDown,\n    mdUp: mdUp,\n    smDown: smDown,\n    smUp: smUp,\n    xlDown: xlDown,\n    xlUp: xlUp,\n    xsDown: xsDown,\n    xsUp: xsUp\n  }, other));\n}\nprocess.env.NODE_ENV !== \"production\" ? Hidden.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Specify which implementation to use.  'js' is the default, 'css' works better for\n   * server-side rendering.\n   * @default 'js'\n   */\n  implementation: PropTypes.oneOf(['css', 'js']),\n  /**\n   * You can use this prop when choosing the `js` implementation with server-side rendering.\n   *\n   * As `window.innerWidth` is unavailable on the server,\n   * we default to rendering an empty component during the first mount.\n   * You might want to use a heuristic to approximate\n   * the screen width of the client browser screen width.\n   *\n   * For instance, you could be using the user-agent or the client-hints.\n   * https://caniuse.com/#search=client%20hint\n   */\n  initialWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  lgDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  lgUp: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  mdDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  mdUp: PropTypes.bool,\n  /**\n   * Hide the given breakpoint(s).\n   */\n  only: PropTypes.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']), PropTypes.arrayOf(PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']).isRequired)]),\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  smDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  smUp: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  xlDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  xlUp: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  xsDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  xsUp: PropTypes.bool\n} : void 0;\nexport default Hidden;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "HiddenJs", "HiddenCss", "jsx", "_jsx", "Hidden", "props", "implementation", "lgDown", "lgUp", "mdDown", "mdUp", "smDown", "smUp", "xlDown", "xlUp", "xsDown", "xsUp", "other", "process", "env", "NODE_ENV", "propTypes", "children", "node", "oneOf", "initialWidth", "bool", "only", "oneOfType", "arrayOf", "isRequired"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/Hidden/Hidden.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"implementation\", \"lgDown\", \"lgUp\", \"mdDown\", \"mdUp\", \"smDown\", \"smUp\", \"xlDown\", \"xlUp\", \"xsDown\", \"xsUp\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport HiddenJs from './HiddenJs';\nimport HiddenCss from './HiddenCss';\n\n/**\n * Responsively hides children based on the selected implementation.\n *\n * @deprecated The Hidden component was deprecated in Material UI v5. To learn more, see [the Hidden section](/material-ui/migration/v5-component-changes/#hidden) of the migration docs.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction Hidden(props) {\n  const {\n      implementation = 'js',\n      lgDown = false,\n      lgUp = false,\n      mdDown = false,\n      mdUp = false,\n      smDown = false,\n      smUp = false,\n      xlDown = false,\n      xlUp = false,\n      xsDown = false,\n      xsUp = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  if (implementation === 'js') {\n    return /*#__PURE__*/_jsx(HiddenJs, _extends({\n      lgDown: lgDown,\n      lgUp: lgUp,\n      mdDown: mdDown,\n      mdUp: mdUp,\n      smDown: smDown,\n      smUp: smUp,\n      xlDown: xlDown,\n      xlUp: xlUp,\n      xsDown: xsDown,\n      xsUp: xsUp\n    }, other));\n  }\n  return /*#__PURE__*/_jsx(HiddenCss, _extends({\n    lgDown: lgDown,\n    lgUp: lgUp,\n    mdDown: mdDown,\n    mdUp: mdUp,\n    smDown: smDown,\n    smUp: smUp,\n    xlDown: xlDown,\n    xlUp: xlUp,\n    xsDown: xsDown,\n    xsUp: xsUp\n  }, other));\n}\nprocess.env.NODE_ENV !== \"production\" ? Hidden.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Specify which implementation to use.  'js' is the default, 'css' works better for\n   * server-side rendering.\n   * @default 'js'\n   */\n  implementation: PropTypes.oneOf(['css', 'js']),\n  /**\n   * You can use this prop when choosing the `js` implementation with server-side rendering.\n   *\n   * As `window.innerWidth` is unavailable on the server,\n   * we default to rendering an empty component during the first mount.\n   * You might want to use a heuristic to approximate\n   * the screen width of the client browser screen width.\n   *\n   * For instance, you could be using the user-agent or the client-hints.\n   * https://caniuse.com/#search=client%20hint\n   */\n  initialWidth: PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']),\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  lgDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  lgUp: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  mdDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  mdUp: PropTypes.bool,\n  /**\n   * Hide the given breakpoint(s).\n   */\n  only: PropTypes.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']), PropTypes.arrayOf(PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl']).isRequired)]),\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  smDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  smUp: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  xlDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  xlUp: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens below (but not including) this size.\n   * @default false\n   */\n  xsDown: PropTypes.bool,\n  /**\n   * If `true`, component is hidden on screens this size and above.\n   * @default false\n   */\n  xsUp: PropTypes.bool\n} : void 0;\nexport default Hidden;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC;AAC9H,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;;AAEnC;AACA;AACA;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,MAAMA,CAACC,KAAK,EAAE;EACrB,MAAM;MACFC,cAAc,GAAG,IAAI;MACrBC,MAAM,GAAG,KAAK;MACdC,IAAI,GAAG,KAAK;MACZC,MAAM,GAAG,KAAK;MACdC,IAAI,GAAG,KAAK;MACZC,MAAM,GAAG,KAAK;MACdC,IAAI,GAAG,KAAK;MACZC,MAAM,GAAG,KAAK;MACdC,IAAI,GAAG,KAAK;MACZC,MAAM,GAAG,KAAK;MACdC,IAAI,GAAG;IACT,CAAC,GAAGX,KAAK;IACTY,KAAK,GAAGrB,6BAA6B,CAACS,KAAK,EAAER,SAAS,CAAC;EACzD,IAAIS,cAAc,KAAK,IAAI,EAAE;IAC3B,OAAO,aAAaH,IAAI,CAACH,QAAQ,EAAEL,QAAQ,CAAC;MAC1CY,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA,IAAI;MACVC,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA,IAAI;MACVC,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA,IAAI;MACVC,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA,IAAI;MACVC,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA;IACR,CAAC,EAAEC,KAAK,CAAC,CAAC;EACZ;EACA,OAAO,aAAad,IAAI,CAACF,SAAS,EAAEN,QAAQ,CAAC;IAC3CY,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC,EAAEC,KAAK,CAAC,CAAC;AACZ;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhB,MAAM,CAACiB,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEvB,SAAS,CAACwB,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEjB,cAAc,EAAEP,SAAS,CAACyB,KAAK,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;EAC9C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,YAAY,EAAE1B,SAAS,CAACyB,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAC7D;AACF;AACA;AACA;EACEjB,MAAM,EAAER,SAAS,CAAC2B,IAAI;EACtB;AACF;AACA;AACA;EACElB,IAAI,EAAET,SAAS,CAAC2B,IAAI;EACpB;AACF;AACA;AACA;EACEjB,MAAM,EAAEV,SAAS,CAAC2B,IAAI;EACtB;AACF;AACA;AACA;EACEhB,IAAI,EAAEX,SAAS,CAAC2B,IAAI;EACpB;AACF;AACA;EACEC,IAAI,EAAE5B,SAAS,CAAC6B,SAAS,CAAC,CAAC7B,SAAS,CAACyB,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEzB,SAAS,CAAC8B,OAAO,CAAC9B,SAAS,CAACyB,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAACM,UAAU,CAAC,CAAC,CAAC;EAC3J;AACF;AACA;AACA;EACEnB,MAAM,EAAEZ,SAAS,CAAC2B,IAAI;EACtB;AACF;AACA;AACA;EACEd,IAAI,EAAEb,SAAS,CAAC2B,IAAI;EACpB;AACF;AACA;AACA;EACEb,MAAM,EAAEd,SAAS,CAAC2B,IAAI;EACtB;AACF;AACA;AACA;EACEZ,IAAI,EAAEf,SAAS,CAAC2B,IAAI;EACpB;AACF;AACA;AACA;EACEX,MAAM,EAAEhB,SAAS,CAAC2B,IAAI;EACtB;AACF;AACA;AACA;EACEV,IAAI,EAAEjB,SAAS,CAAC2B;AAClB,CAAC,GAAG,KAAK,CAAC;AACV,eAAetB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}