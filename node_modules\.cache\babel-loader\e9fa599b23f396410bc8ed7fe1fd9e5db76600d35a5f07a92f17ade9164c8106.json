{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"row\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getFormGroupUtilityClass } from './formGroupClasses';\nimport useFormControl from '../FormControl/useFormControl';\nimport formControlState from '../FormControl/formControlState';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    row,\n    error\n  } = ownerState;\n  const slots = {\n    root: ['root', row && 'row', error && 'error']\n  };\n  return composeClasses(slots, getFormGroupUtilityClass, classes);\n};\nconst FormGroupRoot = styled('div', {\n  name: 'MuiFormGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.row && styles.row];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    display: 'flex',\n    flexDirection: 'column',\n    flexWrap: 'wrap'\n  }, ownerState.row && {\n    flexDirection: 'row'\n  });\n});\n\n/**\n * `FormGroup` wraps controls such as `Checkbox` and `Switch`.\n * It provides compact row layout.\n * For the `Radio`, you should be using the `RadioGroup` component instead of this one.\n */\nconst FormGroup = /*#__PURE__*/React.forwardRef(function FormGroup(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormGroup'\n  });\n  const {\n      className,\n      row = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['error']\n  });\n  const ownerState = _extends({}, props, {\n    row,\n    error: fcs.error\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FormGroupRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Display group of elements in a compact row.\n   * @default false\n   */\n  row: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormGroup;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "getFormGroupUtilityClass", "useFormControl", "formControlState", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "row", "error", "slots", "root", "FormGroupRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "display", "flexDirection", "flexWrap", "FormGroup", "forwardRef", "inProps", "ref", "className", "other", "muiFormControl", "fcs", "states", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "bool", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/FormGroup/FormGroup.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"row\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getFormGroupUtilityClass } from './formGroupClasses';\nimport useFormControl from '../FormControl/useFormControl';\nimport formControlState from '../FormControl/formControlState';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    row,\n    error\n  } = ownerState;\n  const slots = {\n    root: ['root', row && 'row', error && 'error']\n  };\n  return composeClasses(slots, getFormGroupUtilityClass, classes);\n};\nconst FormGroupRoot = styled('div', {\n  name: 'MuiFormGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.row && styles.row];\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'flex',\n  flexDirection: 'column',\n  flexWrap: 'wrap'\n}, ownerState.row && {\n  flexDirection: 'row'\n}));\n\n/**\n * `FormGroup` wraps controls such as `Checkbox` and `Switch`.\n * It provides compact row layout.\n * For the `Radio`, you should be using the `RadioGroup` component instead of this one.\n */\nconst FormGroup = /*#__PURE__*/React.forwardRef(function FormGroup(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormGroup'\n  });\n  const {\n      className,\n      row = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['error']\n  });\n  const ownerState = _extends({}, props, {\n    row,\n    error: fcs.error\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FormGroupRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Display group of elements in a compact row.\n   * @default false\n   */\n  row: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC;AACtC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,wBAAwB,QAAQ,oBAAoB;AAC7D,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,GAAG;IACHC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,GAAG,IAAI,KAAK,EAAEC,KAAK,IAAI,OAAO;EAC/C,CAAC;EACD,OAAOZ,cAAc,CAACa,KAAK,EAAEV,wBAAwB,EAAEO,OAAO,CAAC;AACjE,CAAC;AACD,MAAMK,aAAa,GAAGd,MAAM,CAAC,KAAK,EAAE;EAClCe,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJX;IACF,CAAC,GAAGU,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEL,UAAU,CAACE,GAAG,IAAIS,MAAM,CAACT,GAAG,CAAC;EACpD;AACF,CAAC,CAAC,CAACU,IAAA;EAAA,IAAC;IACFZ;EACF,CAAC,GAAAY,IAAA;EAAA,OAAK1B,QAAQ,CAAC;IACb2B,OAAO,EAAE,MAAM;IACfC,aAAa,EAAE,QAAQ;IACvBC,QAAQ,EAAE;EACZ,CAAC,EAAEf,UAAU,CAACE,GAAG,IAAI;IACnBY,aAAa,EAAE;EACjB,CAAC,CAAC;AAAA,EAAC;;AAEH;AACA;AACA;AACA;AACA;AACA,MAAME,SAAS,GAAG,aAAa5B,KAAK,CAAC6B,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,MAAMT,KAAK,GAAGjB,eAAe,CAAC;IAC5BiB,KAAK,EAAEQ,OAAO;IACdX,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFa,SAAS;MACTlB,GAAG,GAAG;IACR,CAAC,GAAGQ,KAAK;IACTW,KAAK,GAAGpC,6BAA6B,CAACyB,KAAK,EAAEvB,SAAS,CAAC;EACzD,MAAMmC,cAAc,GAAG3B,cAAc,CAAC,CAAC;EACvC,MAAM4B,GAAG,GAAG3B,gBAAgB,CAAC;IAC3Bc,KAAK;IACLY,cAAc;IACdE,MAAM,EAAE,CAAC,OAAO;EAClB,CAAC,CAAC;EACF,MAAMxB,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;IACrCR,GAAG;IACHC,KAAK,EAAEoB,GAAG,CAACpB;EACb,CAAC,CAAC;EACF,MAAMF,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACQ,aAAa,EAAEpB,QAAQ,CAAC;IAC/CkC,SAAS,EAAE9B,IAAI,CAACW,OAAO,CAACI,IAAI,EAAEe,SAAS,CAAC;IACxCpB,UAAU,EAAEA,UAAU;IACtBmB,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,SAAS,CAACY,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAExC,SAAS,CAACyC,IAAI;EACxB;AACF;AACA;EACE7B,OAAO,EAAEZ,SAAS,CAAC0C,MAAM;EACzB;AACF;AACA;EACEX,SAAS,EAAE/B,SAAS,CAAC2C,MAAM;EAC3B;AACF;AACA;AACA;EACE9B,GAAG,EAAEb,SAAS,CAAC4C,IAAI;EACnB;AACF;AACA;EACEC,EAAE,EAAE7C,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAAC+C,OAAO,CAAC/C,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC0C,MAAM,EAAE1C,SAAS,CAAC4C,IAAI,CAAC,CAAC,CAAC,EAAE5C,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAAC0C,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAef,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}