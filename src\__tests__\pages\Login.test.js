import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, mockAuthResponses, validFormData, invalidFormData } from '../utils/testUtils';
import Login from '../../pages/Login';
import authService from '../../api/authService';

// Mock the authService
jest.mock('../../api/authService');
const mockedAuthService = authService;

// Mock the navigate function
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock the components that might have complex implementations
jest.mock('../../components/GoogleOAuthButton', () => {
  return function MockGoogleOAuthButton({ onSuccess, onError, disabled, text }) {
    return (
      <button
        data-testid="google-oauth-button"
        disabled={disabled}
        onClick={() => onSuccess({ code: 'mock-google-code' })}
      >
        {text}
      </button>
    );
  };
});

jest.mock('../../components/OrDivider', () => {
  return function MockOrDivider() {
    return <div data-testid="or-divider">OR</div>;
  };
});

jest.mock('../../components/FormControlWrapper', () => {
  return function MockFormControlWrapper({ name, label, type, placeholder, error, helperText, control }) {
    const { Controller } = require('react-hook-form');
    return (
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <div>
            <label htmlFor={name}>{label}</label>
            <input
              {...field}
              id={name}
              type={type}
              placeholder={placeholder}
              data-testid={`input-${name}`}
            />
            {error && <div data-testid={`error-${name}`}>{helperText}</div>}
          </div>
        )}
      />
    );
  };
});

jest.mock('../../components/PasswordField', () => {
  return function MockPasswordField({ name, label, placeholder, error, helperText, control }) {
    const { Controller } = require('react-hook-form');
    return (
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <div>
            <label htmlFor={name}>{label}</label>
            <input
              {...field}
              id={name}
              type="password"
              placeholder={placeholder}
              data-testid={`input-${name}`}
            />
            {error && <div data-testid={`error-${name}`}>{helperText}</div>}
          </div>
        )}
      />
    );
  };
});

describe('Login Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  describe('Rendering', () => {
    it('should render all form elements', () => {
      renderWithProviders(<Login />);

      expect(screen.getByText('Welcome back')).toBeInTheDocument();
      expect(screen.getByText('Sign in to your account to continue')).toBeInTheDocument();
      expect(screen.getByTestId('google-oauth-button')).toBeInTheDocument();
      expect(screen.getByTestId('or-divider')).toBeInTheDocument();
      expect(screen.getByTestId('input-email')).toBeInTheDocument();
      expect(screen.getByTestId('input-password')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    });

    it('should render navigation links', () => {
      renderWithProviders(<Login />);

      expect(screen.getByText('Forgot password?')).toBeInTheDocument();
      expect(screen.getByText('Sign up')).toBeInTheDocument();
      expect(screen.getByText("Don't have an account?")).toBeInTheDocument();
    });

    it('should render lock icon', () => {
      renderWithProviders(<Login />);

      // The lock icon should be present in the header
      expect(screen.getByTestId('LockOutlinedIcon')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show validation errors for empty fields', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Login />);

      const submitButton = screen.getByRole('button', { name: /sign in/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-email')).toHaveTextContent('Email is required');
        expect(screen.getByTestId('error-password')).toHaveTextContent('Password is required');
      });
    });

    it('should show validation error for invalid email', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      await user.type(emailInput, invalidFormData.login.email);

      const submitButton = screen.getByRole('button', { name: /sign in/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-email')).toHaveTextContent('Invalid email address');
      });
    });

    it('should not show validation errors for valid input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);

      await waitFor(() => {
        expect(screen.queryByTestId('error-email')).not.toBeInTheDocument();
        expect(screen.queryByTestId('error-password')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Submission', () => {
    it('should submit form with valid data and navigate on success', async () => {
      const user = userEvent.setup();
      mockedAuthService.login.mockResolvedValueOnce(mockAuthResponses.loginSuccess.data);

      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockedAuthService.login).toHaveBeenCalledWith(
          validFormData.login,
          expect.any(Function),
          expect.any(Function)
        );
      });

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/profile');
      });
    });

    it('should show loading state during submission', async () => {
      const user = userEvent.setup();
      mockedAuthService.login.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);
      await user.click(submitButton);

      expect(screen.getByText('Signing in...')).toBeInTheDocument();
      expect(submitButton).toBeDisabled();
    });

    it('should handle login error and show error message', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Invalid credentials';
      mockedAuthService.login.mockRejectedValueOnce(errorMessage);

      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      expect(mockNavigate).not.toHaveBeenCalled();
    });

    it('should disable form during submission', async () => {
      const user = userEvent.setup();
      mockedAuthService.login.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      const googleButton = screen.getByTestId('google-oauth-button');

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);
      await user.click(submitButton);

      expect(submitButton).toBeDisabled();
      expect(googleButton).toBeDisabled();
    });
  });

  describe('Google OAuth', () => {
    it('should handle Google OAuth success', async () => {
      const user = userEvent.setup();
      mockedAuthService.googleLogin.mockResolvedValueOnce(mockAuthResponses.loginSuccess.data);

      renderWithProviders(<Login />);

      const googleButton = screen.getByTestId('google-oauth-button');
      await user.click(googleButton);

      await waitFor(() => {
        expect(mockedAuthService.googleLogin).toHaveBeenCalledWith(
          { code: 'mock-google-code' },
          expect.any(Function),
          expect.any(Function)
        );
      });

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/profile');
      });
    });

    it('should handle Google OAuth error', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Google authentication failed';
      mockedAuthService.googleLogin.mockRejectedValueOnce(errorMessage);

      renderWithProviders(<Login />);

      const googleButton = screen.getByTestId('google-oauth-button');
      await user.click(googleButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should close error snackbar when close button is clicked', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Login failed';
      mockedAuthService.login.mockRejectedValueOnce(errorMessage);

      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      const closeButton = screen.getByLabelText('Close');
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByText(errorMessage)).not.toBeInTheDocument();
      });
    });

    it('should auto-hide error snackbar after timeout', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Login failed';
      mockedAuthService.login.mockRejectedValueOnce(errorMessage);

      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      // Wait for auto-hide (6 seconds + buffer)
      await waitFor(() => {
        expect(screen.queryByText(errorMessage)).not.toBeInTheDocument();
      }, { timeout: 7000 });
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels', () => {
      renderWithProviders(<Login />);

      expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
      expect(screen.getByLabelText('Password')).toBeInTheDocument();
    });

    it('should have proper button roles', () => {
      renderWithProviders(<Login />);

      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /continue with google/i })).toBeInTheDocument();
    });

    it('should have proper link roles', () => {
      renderWithProviders(<Login />);

      expect(screen.getByRole('link', { name: /forgot password/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /sign up/i })).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle form submission with network error', async () => {
      const user = userEvent.setup();
      mockedAuthService.login.mockRejectedValueOnce(new Error('Network Error'));

      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Network Error')).toBeInTheDocument();
      });
    });

    it('should handle multiple rapid form submissions', async () => {
      const user = userEvent.setup();
      mockedAuthService.login.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);

      // Try to submit multiple times rapidly
      await user.click(submitButton);
      await user.click(submitButton);
      await user.click(submitButton);

      // Should only call login once due to disabled state
      await waitFor(() => {
        expect(mockedAuthService.login).toHaveBeenCalledTimes(1);
      });
    });

    it('should handle empty response from authService', async () => {
      const user = userEvent.setup();
      mockedAuthService.login.mockResolvedValueOnce(null);

      renderWithProviders(<Login />);

      const emailInput = screen.getByTestId('input-email');
      const passwordInput = screen.getByTestId('input-password');
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, validFormData.login.email);
      await user.type(passwordInput, validFormData.login.password);
      await user.click(submitButton);

      // Should still navigate even with null response
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/profile');
      });
    });
  });
});
