{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport MoreHorizIcon from '../internal/svg-icons/MoreHoriz';\nimport ButtonBase from '../ButtonBase';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst BreadcrumbCollapsedButton = styled(ButtonBase)(_ref => {\n  let {\n    theme\n  } = _ref;\n  return _extends({\n    display: 'flex',\n    marginLeft: \"calc(\".concat(theme.spacing(1), \" * 0.5)\"),\n    marginRight: \"calc(\".concat(theme.spacing(1), \" * 0.5)\")\n  }, theme.palette.mode === 'light' ? {\n    backgroundColor: theme.palette.grey[100],\n    color: theme.palette.grey[700]\n  } : {\n    backgroundColor: theme.palette.grey[700],\n    color: theme.palette.grey[100]\n  }, {\n    borderRadius: 2,\n    '&:hover, &:focus': _extends({}, theme.palette.mode === 'light' ? {\n      backgroundColor: theme.palette.grey[200]\n    } : {\n      backgroundColor: theme.palette.grey[600]\n    }),\n    '&:active': _extends({\n      boxShadow: theme.shadows[0]\n    }, theme.palette.mode === 'light' ? {\n      backgroundColor: emphasize(theme.palette.grey[200], 0.12)\n    } : {\n      backgroundColor: emphasize(theme.palette.grey[600], 0.12)\n    })\n  });\n});\nconst BreadcrumbCollapsedIcon = styled(MoreHorizIcon)({\n  width: 24,\n  height: 16\n});\n\n/**\n * @ignore - internal component.\n */\nfunction BreadcrumbCollapsed(props) {\n  const {\n      slots = {},\n      slotProps = {}\n    } = props,\n    otherProps = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(\"li\", {\n    children: /*#__PURE__*/_jsx(BreadcrumbCollapsedButton, _extends({\n      focusRipple: true\n    }, otherProps, {\n      ownerState: ownerState,\n      children: /*#__PURE__*/_jsx(BreadcrumbCollapsedIcon, _extends({\n        as: slots.CollapsedIcon,\n        ownerState: ownerState\n      }, slotProps.collapsedIcon))\n    }))\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? BreadcrumbCollapsed.propTypes = {\n  /**\n   * The props used for the CollapsedIcon slot.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    collapsedIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the BreadcumbCollapsed.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    CollapsedIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.object\n} : void 0;\nexport default BreadcrumbCollapsed;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "emphasize", "styled", "MoreHorizIcon", "ButtonBase", "jsx", "_jsx", "BreadcrumbCollapsedButton", "_ref", "theme", "display", "marginLeft", "concat", "spacing", "marginRight", "palette", "mode", "backgroundColor", "grey", "color", "borderRadius", "boxShadow", "shadows", "BreadcrumbCollapsedIcon", "width", "height", "BreadcrumbCollapsed", "props", "slots", "slotProps", "otherProps", "ownerState", "children", "focusRipple", "as", "CollapsedIcon", "collapsedIcon", "process", "env", "NODE_ENV", "propTypes", "shape", "oneOfType", "func", "object", "elementType", "sx"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/Breadcrumbs/BreadcrumbCollapsed.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport MoreHorizIcon from '../internal/svg-icons/MoreHoriz';\nimport ButtonBase from '../ButtonBase';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst BreadcrumbCollapsedButton = styled(ButtonBase)(({\n  theme\n}) => _extends({\n  display: 'flex',\n  marginLeft: `calc(${theme.spacing(1)} * 0.5)`,\n  marginRight: `calc(${theme.spacing(1)} * 0.5)`\n}, theme.palette.mode === 'light' ? {\n  backgroundColor: theme.palette.grey[100],\n  color: theme.palette.grey[700]\n} : {\n  backgroundColor: theme.palette.grey[700],\n  color: theme.palette.grey[100]\n}, {\n  borderRadius: 2,\n  '&:hover, &:focus': _extends({}, theme.palette.mode === 'light' ? {\n    backgroundColor: theme.palette.grey[200]\n  } : {\n    backgroundColor: theme.palette.grey[600]\n  }),\n  '&:active': _extends({\n    boxShadow: theme.shadows[0]\n  }, theme.palette.mode === 'light' ? {\n    backgroundColor: emphasize(theme.palette.grey[200], 0.12)\n  } : {\n    backgroundColor: emphasize(theme.palette.grey[600], 0.12)\n  })\n}));\nconst BreadcrumbCollapsedIcon = styled(MoreHorizIcon)({\n  width: 24,\n  height: 16\n});\n\n/**\n * @ignore - internal component.\n */\nfunction BreadcrumbCollapsed(props) {\n  const {\n      slots = {},\n      slotProps = {}\n    } = props,\n    otherProps = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(\"li\", {\n    children: /*#__PURE__*/_jsx(BreadcrumbCollapsedButton, _extends({\n      focusRipple: true\n    }, otherProps, {\n      ownerState: ownerState,\n      children: /*#__PURE__*/_jsx(BreadcrumbCollapsedIcon, _extends({\n        as: slots.CollapsedIcon,\n        ownerState: ownerState\n      }, slotProps.collapsedIcon))\n    }))\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? BreadcrumbCollapsed.propTypes = {\n  /**\n   * The props used for the CollapsedIcon slot.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    collapsedIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the BreadcumbCollapsed.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    CollapsedIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.object\n} : void 0;\nexport default BreadcrumbCollapsed;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,QAAQ,8BAA8B;AACxD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,iCAAiC;AAC3D,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,yBAAyB,GAAGL,MAAM,CAACE,UAAU,CAAC,CAACI,IAAA;EAAA,IAAC;IACpDC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAKX,QAAQ,CAAC;IACba,OAAO,EAAE,MAAM;IACfC,UAAU,UAAAC,MAAA,CAAUH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC,YAAS;IAC7CC,WAAW,UAAAF,MAAA,CAAUH,KAAK,CAACI,OAAO,CAAC,CAAC,CAAC;EACvC,CAAC,EAAEJ,KAAK,CAACM,OAAO,CAACC,IAAI,KAAK,OAAO,GAAG;IAClCC,eAAe,EAAER,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC;IACxCC,KAAK,EAAEV,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG;EAC/B,CAAC,GAAG;IACFD,eAAe,EAAER,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC;IACxCC,KAAK,EAAEV,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG;EAC/B,CAAC,EAAE;IACDE,YAAY,EAAE,CAAC;IACf,kBAAkB,EAAEvB,QAAQ,CAAC,CAAC,CAAC,EAAEY,KAAK,CAACM,OAAO,CAACC,IAAI,KAAK,OAAO,GAAG;MAChEC,eAAe,EAAER,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG;IACzC,CAAC,GAAG;MACFD,eAAe,EAAER,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG;IACzC,CAAC,CAAC;IACF,UAAU,EAAErB,QAAQ,CAAC;MACnBwB,SAAS,EAAEZ,KAAK,CAACa,OAAO,CAAC,CAAC;IAC5B,CAAC,EAAEb,KAAK,CAACM,OAAO,CAACC,IAAI,KAAK,OAAO,GAAG;MAClCC,eAAe,EAAEhB,SAAS,CAACQ,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI;IAC1D,CAAC,GAAG;MACFD,eAAe,EAAEhB,SAAS,CAACQ,KAAK,CAACM,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI;IAC1D,CAAC;EACH,CAAC,CAAC;AAAA,EAAC;AACH,MAAMK,uBAAuB,GAAGrB,MAAM,CAACC,aAAa,CAAC,CAAC;EACpDqB,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE;AACV,CAAC,CAAC;;AAEF;AACA;AACA;AACA,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EAClC,MAAM;MACFC,KAAK,GAAG,CAAC,CAAC;MACVC,SAAS,GAAG,CAAC;IACf,CAAC,GAAGF,KAAK;IACTG,UAAU,GAAGlC,6BAA6B,CAAC+B,KAAK,EAAE7B,SAAS,CAAC;EAC9D,MAAMiC,UAAU,GAAGJ,KAAK;EACxB,OAAO,aAAarB,IAAI,CAAC,IAAI,EAAE;IAC7B0B,QAAQ,EAAE,aAAa1B,IAAI,CAACC,yBAAyB,EAAEV,QAAQ,CAAC;MAC9DoC,WAAW,EAAE;IACf,CAAC,EAAEH,UAAU,EAAE;MACbC,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAE,aAAa1B,IAAI,CAACiB,uBAAuB,EAAE1B,QAAQ,CAAC;QAC5DqC,EAAE,EAAEN,KAAK,CAACO,aAAa;QACvBJ,UAAU,EAAEA;MACd,CAAC,EAAEF,SAAS,CAACO,aAAa,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGb,mBAAmB,CAACc,SAAS,GAAG;EACtE;AACF;AACA;AACA;EACEX,SAAS,EAAE7B,SAAS,CAACyC,KAAK,CAAC;IACzBL,aAAa,EAAEpC,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAAC4C,MAAM,CAAC;EACvE,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEhB,KAAK,EAAE5B,SAAS,CAACyC,KAAK,CAAC;IACrBN,aAAa,EAAEnC,SAAS,CAAC6C;EAC3B,CAAC,CAAC;EACF;AACF;AACA;EACEC,EAAE,EAAE9C,SAAS,CAAC4C;AAChB,CAAC,GAAG,KAAK,CAAC;AACV,eAAelB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}