{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"elevation\", \"square\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport getOverlayAlpha from '../styles/getOverlayAlpha';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport { getPaperUtilityClass } from './paperClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && \"elevation\".concat(elevation)]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[\"elevation\".concat(ownerState.elevation)]];\n  }\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  var _theme$vars$overlays;\n  return _extends({\n    backgroundColor: (theme.vars || theme).palette.background.paper,\n    color: (theme.vars || theme).palette.text.primary,\n    transition: theme.transitions.create('box-shadow')\n  }, !ownerState.square && {\n    borderRadius: theme.shape.borderRadius\n  }, ownerState.variant === 'outlined' && {\n    border: \"1px solid \".concat((theme.vars || theme).palette.divider)\n  }, ownerState.variant === 'elevation' && _extends({\n    boxShadow: (theme.vars || theme).shadows[ownerState.elevation]\n  }, !theme.vars && theme.palette.mode === 'dark' && {\n    backgroundImage: \"linear-gradient(\".concat(alpha('#fff', getOverlayAlpha(ownerState.elevation)), \", \").concat(alpha('#fff', getOverlayAlpha(ownerState.elevation)), \")\")\n  }, theme.vars && {\n    backgroundImage: (_theme$vars$overlays = theme.vars.overlays) == null ? void 0 : _theme$vars$overlays[ownerState.elevation]\n  }));\n});\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const {\n      className,\n      component = 'div',\n      elevation = 1,\n      square = false,\n      variant = 'elevation'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    elevation,\n    square,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const theme = useTheme();\n    if (theme.shadows[elevation] === undefined) {\n      console.error([\"MUI: The elevation provided <Paper elevation={\".concat(elevation, \"}> is not available in the theme.\"), \"Please make sure that `theme.shadows[\".concat(elevation, \"]` is defined.\")].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(\"MUI: Combining `elevation={\".concat(elevation, \"}` with `variant=\\\"\").concat(variant, \"\\\"` has no effect. Either use `elevation={0}` or use a different `variant`.\"));\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "integerPropType", "chainPropTypes", "composeClasses", "alpha", "styled", "getOverlayAlpha", "useDefaultProps", "useTheme", "getPaperUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "square", "elevation", "variant", "classes", "slots", "root", "concat", "PaperRoot", "name", "slot", "overridesResolver", "props", "styles", "rounded", "_ref", "theme", "_theme$vars$overlays", "backgroundColor", "vars", "palette", "background", "paper", "color", "text", "primary", "transition", "transitions", "create", "borderRadius", "shape", "border", "divider", "boxShadow", "shadows", "mode", "backgroundImage", "overlays", "Paper", "forwardRef", "inProps", "ref", "className", "component", "other", "process", "env", "NODE_ENV", "undefined", "console", "error", "join", "as", "propTypes", "children", "node", "object", "string", "elementType", "Error", "bool", "sx", "oneOfType", "arrayOf", "func", "oneOf"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/Paper/Paper.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"elevation\", \"square\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport getOverlayAlpha from '../styles/getOverlayAlpha';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport { getPaperUtilityClass } from './paperClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && `elevation${elevation}`]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[`elevation${ownerState.elevation}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$vars$overlays;\n  return _extends({\n    backgroundColor: (theme.vars || theme).palette.background.paper,\n    color: (theme.vars || theme).palette.text.primary,\n    transition: theme.transitions.create('box-shadow')\n  }, !ownerState.square && {\n    borderRadius: theme.shape.borderRadius\n  }, ownerState.variant === 'outlined' && {\n    border: `1px solid ${(theme.vars || theme).palette.divider}`\n  }, ownerState.variant === 'elevation' && _extends({\n    boxShadow: (theme.vars || theme).shadows[ownerState.elevation]\n  }, !theme.vars && theme.palette.mode === 'dark' && {\n    backgroundImage: `linear-gradient(${alpha('#fff', getOverlayAlpha(ownerState.elevation))}, ${alpha('#fff', getOverlayAlpha(ownerState.elevation))})`\n  }, theme.vars && {\n    backgroundImage: (_theme$vars$overlays = theme.vars.overlays) == null ? void 0 : _theme$vars$overlays[ownerState.elevation]\n  }));\n});\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const {\n      className,\n      component = 'div',\n      elevation = 1,\n      square = false,\n      variant = 'elevation'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    elevation,\n    square,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const theme = useTheme();\n    if (theme.shadows[elevation] === undefined) {\n      console.error([`MUI: The elevation provided <Paper elevation={${elevation}}> is not available in the theme.`, `Please make sure that \\`theme.shadows[${elevation}]\\` is defined.`].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(`MUI: Combining \\`elevation={${elevation}}\\` with \\`variant=\"${variant}\"\\` has no effect. Either use \\`elevation={0}\\` or use a different \\`variant\\`.`);\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC;AAC9E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,OAAO,EAAE,CAACF,MAAM,IAAI,SAAS,EAAEE,OAAO,KAAK,WAAW,gBAAAI,MAAA,CAAgBL,SAAS,CAAE;EAClG,CAAC;EACD,OAAOZ,cAAc,CAACe,KAAK,EAAET,oBAAoB,EAAEQ,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMI,SAAS,GAAGhB,MAAM,CAAC,KAAK,EAAE;EAC9BiB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJb;IACF,CAAC,GAAGY,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,IAAI,EAAEO,MAAM,CAACb,UAAU,CAACG,OAAO,CAAC,EAAE,CAACH,UAAU,CAACC,MAAM,IAAIY,MAAM,CAACC,OAAO,EAAEd,UAAU,CAACG,OAAO,KAAK,WAAW,IAAIU,MAAM,aAAAN,MAAA,CAAaP,UAAU,CAACE,SAAS,EAAG,CAAC;EAC1K;AACF,CAAC,CAAC,CAACa,IAAA,IAGG;EAAA,IAHF;IACFC,KAAK;IACLhB;EACF,CAAC,GAAAe,IAAA;EACC,IAAIE,oBAAoB;EACxB,OAAOlC,QAAQ,CAAC;IACdmC,eAAe,EAAE,CAACF,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACC,UAAU,CAACC,KAAK;IAC/DC,KAAK,EAAE,CAACP,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACI,IAAI,CAACC,OAAO;IACjDC,UAAU,EAAEV,KAAK,CAACW,WAAW,CAACC,MAAM,CAAC,YAAY;EACnD,CAAC,EAAE,CAAC5B,UAAU,CAACC,MAAM,IAAI;IACvB4B,YAAY,EAAEb,KAAK,CAACc,KAAK,CAACD;EAC5B,CAAC,EAAE7B,UAAU,CAACG,OAAO,KAAK,UAAU,IAAI;IACtC4B,MAAM,eAAAxB,MAAA,CAAe,CAACS,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACY,OAAO;EAC5D,CAAC,EAAEhC,UAAU,CAACG,OAAO,KAAK,WAAW,IAAIpB,QAAQ,CAAC;IAChDkD,SAAS,EAAE,CAACjB,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEkB,OAAO,CAAClC,UAAU,CAACE,SAAS;EAC/D,CAAC,EAAE,CAACc,KAAK,CAACG,IAAI,IAAIH,KAAK,CAACI,OAAO,CAACe,IAAI,KAAK,MAAM,IAAI;IACjDC,eAAe,qBAAA7B,MAAA,CAAqBhB,KAAK,CAAC,MAAM,EAAEE,eAAe,CAACO,UAAU,CAACE,SAAS,CAAC,CAAC,QAAAK,MAAA,CAAKhB,KAAK,CAAC,MAAM,EAAEE,eAAe,CAACO,UAAU,CAACE,SAAS,CAAC,CAAC;EACnJ,CAAC,EAAEc,KAAK,CAACG,IAAI,IAAI;IACfiB,eAAe,EAAE,CAACnB,oBAAoB,GAAGD,KAAK,CAACG,IAAI,CAACkB,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGpB,oBAAoB,CAACjB,UAAU,CAACE,SAAS;EAC5H,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAMoC,KAAK,GAAG,aAAarD,KAAK,CAACsD,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,MAAM7B,KAAK,GAAGlB,eAAe,CAAC;IAC5BkB,KAAK,EAAE4B,OAAO;IACd/B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFiC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBzC,SAAS,GAAG,CAAC;MACbD,MAAM,GAAG,KAAK;MACdE,OAAO,GAAG;IACZ,CAAC,GAAGS,KAAK;IACTgC,KAAK,GAAG9D,6BAA6B,CAAC8B,KAAK,EAAE5B,SAAS,CAAC;EACzD,MAAMgB,UAAU,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;IACrC+B,SAAS;IACTzC,SAAS;IACTD,MAAM;IACNE;EACF,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGL,iBAAiB,CAACC,UAAU,CAAC;EAC7C,IAAI6C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAM/B,KAAK,GAAGrB,QAAQ,CAAC,CAAC;IACxB,IAAIqB,KAAK,CAACkB,OAAO,CAAChC,SAAS,CAAC,KAAK8C,SAAS,EAAE;MAC1CC,OAAO,CAACC,KAAK,CAAC,kDAAA3C,MAAA,CAAkDL,SAAS,gFAAAK,MAAA,CAA8EL,SAAS,oBAAkB,CAACiD,IAAI,CAAC,IAAI,CAAC,CAAC;IAChM;EACF;EACA,OAAO,aAAarD,IAAI,CAACU,SAAS,EAAEzB,QAAQ,CAAC;IAC3CqE,EAAE,EAAET,SAAS;IACb3C,UAAU,EAAEA,UAAU;IACtB0C,SAAS,EAAEvD,IAAI,CAACiB,OAAO,CAACE,IAAI,EAAEoC,SAAS,CAAC;IACxCD,GAAG,EAAEA;EACP,CAAC,EAAEG,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGT,KAAK,CAACe,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEpE,SAAS,CAACqE,IAAI;EACxB;AACF;AACA;EACEnD,OAAO,EAAElB,SAAS,CAACsE,MAAM;EACzB;AACF;AACA;EACEd,SAAS,EAAExD,SAAS,CAACuE,MAAM;EAC3B;AACF;AACA;AACA;EACEd,SAAS,EAAEzD,SAAS,CAACwE,WAAW;EAChC;AACF;AACA;AACA;AACA;EACExD,SAAS,EAAEb,cAAc,CAACD,eAAe,EAAEwB,KAAK,IAAI;IAClD,MAAM;MACJV,SAAS;MACTC;IACF,CAAC,GAAGS,KAAK;IACT,IAAIV,SAAS,GAAG,CAAC,IAAIC,OAAO,KAAK,UAAU,EAAE;MAC3C,OAAO,IAAIwD,KAAK,+BAAApD,MAAA,CAAgCL,SAAS,yBAAAK,MAAA,CAAuBJ,OAAO,gFAAiF,CAAC;IAC3K;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEF,MAAM,EAAEf,SAAS,CAAC0E,IAAI;EACtB;AACF;AACA;EACEC,EAAE,EAAE3E,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC6E,OAAO,CAAC7E,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAACsE,MAAM,EAAEtE,SAAS,CAAC0E,IAAI,CAAC,CAAC,CAAC,EAAE1E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAACsE,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACErD,OAAO,EAAEjB,SAAS,CAAC,sCAAsC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC+E,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,EAAE/E,SAAS,CAACuE,MAAM,CAAC;AACnI,CAAC,GAAG,KAAK,CAAC;AACV,eAAenB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}