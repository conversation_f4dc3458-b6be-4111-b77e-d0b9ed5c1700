{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getInputAdornmentUtilityClass(slot) {\n  return generateUtilityClass('MuiInputAdornment', slot);\n}\nconst inputAdornmentClasses = generateUtilityClasses('MuiInputAdornment', ['root', 'filled', 'standard', 'outlined', 'positionStart', 'positionEnd', 'disablePointerEvents', 'hiddenLabel', 'sizeSmall']);\nexport default inputAdornmentClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getInputAdornmentUtilityClass", "slot", "inputAdornmentClasses"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/InputAdornment/inputAdornmentClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getInputAdornmentUtilityClass(slot) {\n  return generateUtilityClass('MuiInputAdornment', slot);\n}\nconst inputAdornmentClasses = generateUtilityClasses('MuiInputAdornment', ['root', 'filled', 'standard', 'outlined', 'positionStart', 'positionEnd', 'disablePointerEvents', 'hiddenLabel', 'sizeSmall']);\nexport default inputAdornmentClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOF,oBAAoB,CAAC,mBAAmB,EAAEE,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGJ,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,sBAAsB,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;AACzM,eAAeI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}