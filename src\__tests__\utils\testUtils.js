import React from 'react';
import { render } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material';
import CssBaseline from '@mui/material/CssBaseline';

// Create a test theme similar to the app theme
const testTheme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Custom render function that includes providers
export const renderWithProviders = (ui, options = {}) => {
  const { initialEntries = ['/'], ...renderOptions } = options;

  const Wrapper = ({ children }) => (
    <BrowserRouter>
      <ThemeProvider theme={testTheme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </BrowserRouter>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock user data for testing
export const mockUserData = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  createdAt: '2024-01-01T00:00:00.000Z',
};

// Mock authentication responses
export const mockAuthResponses = {
  loginSuccess: {
    data: {
      token: 'mock-jwt-token',
      user: mockUserData,
      message: 'Login successful',
    },
  },
  signupSuccess: {
    data: {
      user: mockUserData,
      message: 'Account created successfully',
    },
  },
  profileSuccess: {
    data: mockUserData,
  },
  forgotPasswordSuccess: {
    data: {
      message: 'Password reset email sent',
    },
  },
  error: {
    response: {
      data: {
        message: 'Authentication failed',
      },
    },
  },
  networkError: {
    message: 'Network Error',
  },
};

// Helper function to mock localStorage
export const mockLocalStorage = (items = {}) => {
  const localStorageMock = {
    getItem: jest.fn((key) => items[key] || null),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  };
  
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true,
  });
  
  return localStorageMock;
};

// Helper function to wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0));

// Common test data
export const validFormData = {
  login: {
    email: '<EMAIL>',
    password: 'password123',
  },
  signup: {
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'Password123!',
    confirmPassword: 'Password123!',
  },
  forgotPassword: {
    email: '<EMAIL>',
  },
};

export const invalidFormData = {
  login: {
    email: 'invalid-email',
    password: '',
  },
  signup: {
    name: '',
    email: 'invalid-email',
    password: '123',
    confirmPassword: '456',
  },
  forgotPassword: {
    email: 'invalid-email',
  },
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
