{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"value\"],\n  _excluded2 = [\"className\", \"defaultValue\", \"disabled\", \"emptyIcon\", \"emptyLabelText\", \"getLabelText\", \"highlightSelectedOnly\", \"icon\", \"IconContainerComponent\", \"max\", \"name\", \"onChange\", \"onChangeActive\", \"onMouseLeave\", \"onMouseMove\", \"precision\", \"readOnly\", \"size\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport clamp from '@mui/utils/clamp';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { capitalize, useForkRef, useIsFocusVisible, useControlled, unstable_useId as useId } from '../utils';\nimport Star from '../internal/svg-icons/Star';\nimport StarBorder from '../internal/svg-icons/StarBorder';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled, { slotShouldForwardProp } from '../styles/styled';\nimport ratingClasses, { getRatingUtilityClass } from './ratingClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction getDecimalPrecision(num) {\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToPrecision(value, precision) {\n  if (value == null) {\n    return value;\n  }\n  const nearest = Math.round(value / precision) * precision;\n  return Number(nearest.toFixed(getDecimalPrecision(precision)));\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    size,\n    readOnly,\n    disabled,\n    emptyValueFocused,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', \"size\".concat(capitalize(size)), disabled && 'disabled', focusVisible && 'focusVisible', readOnly && 'readOnly'],\n    label: ['label', 'pristine'],\n    labelEmptyValue: [emptyValueFocused && 'labelEmptyValueActive'],\n    icon: ['icon'],\n    iconEmpty: ['iconEmpty'],\n    iconFilled: ['iconFilled'],\n    iconHover: ['iconHover'],\n    iconFocus: ['iconFocus'],\n    iconActive: ['iconActive'],\n    decimal: ['decimal'],\n    visuallyHidden: ['visuallyHidden']\n  };\n  return composeClasses(slots, getRatingUtilityClass, classes);\n};\nconst RatingRoot = styled('span', {\n  name: 'MuiRating',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [\"& .\".concat(ratingClasses.visuallyHidden)]: styles.visuallyHidden\n    }, styles.root, styles[\"size\".concat(capitalize(ownerState.size))], ownerState.readOnly && styles.readOnly];\n  }\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    display: 'inline-flex',\n    // Required to position the pristine input absolutely\n    position: 'relative',\n    fontSize: theme.typography.pxToRem(24),\n    color: '#faaf00',\n    cursor: 'pointer',\n    textAlign: 'left',\n    width: 'min-content',\n    WebkitTapHighlightColor: 'transparent',\n    [\"&.\".concat(ratingClasses.disabled)]: {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [\"&.\".concat(ratingClasses.focusVisible, \" .\").concat(ratingClasses.iconActive)]: {\n      outline: '1px solid #999'\n    },\n    [\"& .\".concat(ratingClasses.visuallyHidden)]: visuallyHidden\n  }, ownerState.size === 'small' && {\n    fontSize: theme.typography.pxToRem(18)\n  }, ownerState.size === 'large' && {\n    fontSize: theme.typography.pxToRem(30)\n  }, ownerState.readOnly && {\n    pointerEvents: 'none'\n  });\n});\nconst RatingLabel = styled('label', {\n  name: 'MuiRating',\n  slot: 'Label',\n  overridesResolver: (_ref2, styles) => {\n    let {\n      ownerState\n    } = _ref2;\n    return [styles.label, ownerState.emptyValueFocused && styles.labelEmptyValueActive];\n  }\n})(_ref3 => {\n  let {\n    ownerState\n  } = _ref3;\n  return _extends({\n    cursor: 'inherit'\n  }, ownerState.emptyValueFocused && {\n    top: 0,\n    bottom: 0,\n    position: 'absolute',\n    outline: '1px solid #999',\n    width: '100%'\n  });\n});\nconst RatingIcon = styled('span', {\n  name: 'MuiRating',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.iconEmpty && styles.iconEmpty, ownerState.iconFilled && styles.iconFilled, ownerState.iconHover && styles.iconHover, ownerState.iconFocus && styles.iconFocus, ownerState.iconActive && styles.iconActive];\n  }\n})(_ref4 => {\n  let {\n    theme,\n    ownerState\n  } = _ref4;\n  return _extends({\n    // Fit wrapper to actual icon size.\n    display: 'flex',\n    transition: theme.transitions.create('transform', {\n      duration: theme.transitions.duration.shortest\n    }),\n    // Fix mouseLeave issue.\n    // https://github.com/facebook/react/issues/4492\n    pointerEvents: 'none'\n  }, ownerState.iconActive && {\n    transform: 'scale(1.2)'\n  }, ownerState.iconEmpty && {\n    color: (theme.vars || theme).palette.action.disabled\n  });\n});\nconst RatingDecimal = styled('span', {\n  name: 'MuiRating',\n  slot: 'Decimal',\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'iconActive',\n  overridesResolver: (props, styles) => {\n    const {\n      iconActive\n    } = props;\n    return [styles.decimal, iconActive && styles.iconActive];\n  }\n})(_ref5 => {\n  let {\n    iconActive\n  } = _ref5;\n  return _extends({\n    position: 'relative'\n  }, iconActive && {\n    transform: 'scale(1.2)'\n  });\n});\nfunction IconContainer(props) {\n  const other = _objectWithoutPropertiesLoose(props, _excluded);\n  return /*#__PURE__*/_jsx(\"span\", _extends({}, other));\n}\nprocess.env.NODE_ENV !== \"production\" ? IconContainer.propTypes = {\n  value: PropTypes.number.isRequired\n} : void 0;\nfunction RatingItem(props) {\n  const {\n    classes,\n    disabled,\n    emptyIcon,\n    focus,\n    getLabelText,\n    highlightSelectedOnly,\n    hover,\n    icon,\n    IconContainerComponent,\n    isActive,\n    itemValue,\n    labelProps,\n    name,\n    onBlur,\n    onChange,\n    onClick,\n    onFocus,\n    readOnly,\n    ownerState,\n    ratingValue,\n    ratingValueRounded\n  } = props;\n  const isFilled = highlightSelectedOnly ? itemValue === ratingValue : itemValue <= ratingValue;\n  const isHovered = itemValue <= hover;\n  const isFocused = itemValue <= focus;\n  const isChecked = itemValue === ratingValueRounded;\n  const id = useId();\n  const container = /*#__PURE__*/_jsx(RatingIcon, {\n    as: IconContainerComponent,\n    value: itemValue,\n    className: clsx(classes.icon, isFilled ? classes.iconFilled : classes.iconEmpty, isHovered && classes.iconHover, isFocused && classes.iconFocus, isActive && classes.iconActive),\n    ownerState: _extends({}, ownerState, {\n      iconEmpty: !isFilled,\n      iconFilled: isFilled,\n      iconHover: isHovered,\n      iconFocus: isFocused,\n      iconActive: isActive\n    }),\n    children: emptyIcon && !isFilled ? emptyIcon : icon\n  });\n  if (readOnly) {\n    return /*#__PURE__*/_jsx(\"span\", _extends({}, labelProps, {\n      children: container\n    }));\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsxs(RatingLabel, _extends({\n      ownerState: _extends({}, ownerState, {\n        emptyValueFocused: undefined\n      }),\n      htmlFor: id\n    }, labelProps, {\n      children: [container, /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: getLabelText(itemValue)\n      })]\n    })), /*#__PURE__*/_jsx(\"input\", {\n      className: classes.visuallyHidden,\n      onFocus: onFocus,\n      onBlur: onBlur,\n      onChange: onChange,\n      onClick: onClick,\n      disabled: disabled,\n      value: itemValue,\n      id: id,\n      type: \"radio\",\n      name: name,\n      checked: isChecked\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RatingItem.propTypes = {\n  classes: PropTypes.object.isRequired,\n  disabled: PropTypes.bool.isRequired,\n  emptyIcon: PropTypes.node,\n  focus: PropTypes.number.isRequired,\n  getLabelText: PropTypes.func.isRequired,\n  highlightSelectedOnly: PropTypes.bool.isRequired,\n  hover: PropTypes.number.isRequired,\n  icon: PropTypes.node,\n  IconContainerComponent: PropTypes.elementType.isRequired,\n  isActive: PropTypes.bool.isRequired,\n  itemValue: PropTypes.number.isRequired,\n  labelProps: PropTypes.object,\n  name: PropTypes.string,\n  onBlur: PropTypes.func.isRequired,\n  onChange: PropTypes.func.isRequired,\n  onClick: PropTypes.func.isRequired,\n  onFocus: PropTypes.func.isRequired,\n  ownerState: PropTypes.object.isRequired,\n  ratingValue: PropTypes.number,\n  ratingValueRounded: PropTypes.number,\n  readOnly: PropTypes.bool.isRequired\n} : void 0;\nconst defaultIcon = /*#__PURE__*/_jsx(Star, {\n  fontSize: \"inherit\"\n});\nconst defaultEmptyIcon = /*#__PURE__*/_jsx(StarBorder, {\n  fontSize: \"inherit\"\n});\nfunction defaultLabelText(value) {\n  return \"\".concat(value, \" Star\").concat(value !== 1 ? 's' : '');\n}\nconst Rating = /*#__PURE__*/React.forwardRef(function Rating(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiRating',\n    props: inProps\n  });\n  const {\n      className,\n      defaultValue = null,\n      disabled = false,\n      emptyIcon = defaultEmptyIcon,\n      emptyLabelText = 'Empty',\n      getLabelText = defaultLabelText,\n      highlightSelectedOnly = false,\n      icon = defaultIcon,\n      IconContainerComponent = IconContainer,\n      max = 5,\n      name: nameProp,\n      onChange,\n      onChangeActive,\n      onMouseLeave,\n      onMouseMove,\n      precision = 1,\n      readOnly = false,\n      size = 'medium',\n      value: valueProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const name = useId(nameProp);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Rating'\n  });\n  const valueRounded = roundValueToPrecision(valueDerived, precision);\n  const isRtl = useRtl();\n  const [{\n    hover,\n    focus\n  }, setState] = React.useState({\n    hover: -1,\n    focus: -1\n  });\n  let value = valueRounded;\n  if (hover !== -1) {\n    value = hover;\n  }\n  if (focus !== -1) {\n    value = focus;\n  }\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const rootRef = React.useRef();\n  const handleRef = useForkRef(focusVisibleRef, rootRef, ref);\n  const handleMouseMove = event => {\n    if (onMouseMove) {\n      onMouseMove(event);\n    }\n    const rootNode = rootRef.current;\n    const {\n      right,\n      left,\n      width: containerWidth\n    } = rootNode.getBoundingClientRect();\n    let percent;\n    if (isRtl) {\n      percent = (right - event.clientX) / containerWidth;\n    } else {\n      percent = (event.clientX - left) / containerWidth;\n    }\n    let newHover = roundValueToPrecision(max * percent + precision / 2, precision);\n    newHover = clamp(newHover, precision, max);\n    setState(prev => prev.hover === newHover && prev.focus === newHover ? prev : {\n      hover: newHover,\n      focus: newHover\n    });\n    setFocusVisible(false);\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleMouseLeave = event => {\n    if (onMouseLeave) {\n      onMouseLeave(event);\n    }\n    const newHover = -1;\n    setState({\n      hover: newHover,\n      focus: newHover\n    });\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleChange = event => {\n    let newValue = event.target.value === '' ? null : parseFloat(event.target.value);\n\n    // Give mouse priority over keyboard\n    // Fix https://github.com/mui/material-ui/issues/22827\n    if (hover !== -1) {\n      newValue = hover;\n    }\n    setValueState(newValue);\n    if (onChange) {\n      onChange(event, newValue);\n    }\n  };\n  const handleClear = event => {\n    // Ignore keyboard events\n    // https://github.com/facebook/react/issues/7407\n    if (event.clientX === 0 && event.clientY === 0) {\n      return;\n    }\n    setState({\n      hover: -1,\n      focus: -1\n    });\n    setValueState(null);\n    if (onChange && parseFloat(event.target.value) === valueRounded) {\n      onChange(event, null);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    const newFocus = parseFloat(event.target.value);\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const handleBlur = event => {\n    if (hover !== -1) {\n      return;\n    }\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    const newFocus = -1;\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const [emptyValueFocused, setEmptyValueFocused] = React.useState(false);\n  const ownerState = _extends({}, props, {\n    defaultValue,\n    disabled,\n    emptyIcon,\n    emptyLabelText,\n    emptyValueFocused,\n    focusVisible,\n    getLabelText,\n    icon,\n    IconContainerComponent,\n    max,\n    precision,\n    readOnly,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(RatingRoot, _extends({\n    ref: handleRef,\n    onMouseMove: handleMouseMove,\n    onMouseLeave: handleMouseLeave,\n    className: clsx(classes.root, className, readOnly && 'MuiRating-readOnly'),\n    ownerState: ownerState,\n    role: readOnly ? 'img' : null,\n    \"aria-label\": readOnly ? getLabelText(value) : null\n  }, other, {\n    children: [Array.from(new Array(max)).map((_, index) => {\n      const itemValue = index + 1;\n      const ratingItemProps = {\n        classes,\n        disabled,\n        emptyIcon,\n        focus,\n        getLabelText,\n        highlightSelectedOnly,\n        hover,\n        icon,\n        IconContainerComponent,\n        name,\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onClick: handleClear,\n        onFocus: handleFocus,\n        ratingValue: value,\n        ratingValueRounded: valueRounded,\n        readOnly,\n        ownerState\n      };\n      const isActive = itemValue === Math.ceil(value) && (hover !== -1 || focus !== -1);\n      if (precision < 1) {\n        const items = Array.from(new Array(1 / precision));\n        return /*#__PURE__*/_jsx(RatingDecimal, {\n          className: clsx(classes.decimal, isActive && classes.iconActive),\n          ownerState: ownerState,\n          iconActive: isActive,\n          children: items.map(($, indexDecimal) => {\n            const itemDecimalValue = roundValueToPrecision(itemValue - 1 + (indexDecimal + 1) * precision, precision);\n            return /*#__PURE__*/_jsx(RatingItem, _extends({}, ratingItemProps, {\n              // The icon is already displayed as active\n              isActive: false,\n              itemValue: itemDecimalValue,\n              labelProps: {\n                style: items.length - 1 === indexDecimal ? {} : {\n                  width: itemDecimalValue === value ? \"\".concat((indexDecimal + 1) * precision * 100, \"%\") : '0%',\n                  overflow: 'hidden',\n                  position: 'absolute'\n                }\n              }\n            }), itemDecimalValue);\n          })\n        }, itemValue);\n      }\n      return /*#__PURE__*/_jsx(RatingItem, _extends({}, ratingItemProps, {\n        isActive: isActive,\n        itemValue: itemValue\n      }), itemValue);\n    }), !readOnly && !disabled && /*#__PURE__*/_jsxs(RatingLabel, {\n      className: clsx(classes.label, classes.labelEmptyValue),\n      ownerState: ownerState,\n      children: [/*#__PURE__*/_jsx(\"input\", {\n        className: classes.visuallyHidden,\n        value: \"\",\n        id: \"\".concat(name, \"-empty\"),\n        type: \"radio\",\n        name: name,\n        checked: valueRounded == null,\n        onFocus: () => setEmptyValueFocused(true),\n        onBlur: () => setEmptyValueFocused(false),\n        onChange: handleChange\n      }), /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: emptyLabelText\n      })]\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Rating.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default null\n   */\n  defaultValue: PropTypes.number,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The icon to display when empty.\n   * @default <StarBorder fontSize=\"inherit\" />\n   */\n  emptyIcon: PropTypes.node,\n  /**\n   * The label read when the rating input is empty.\n   * @default 'Empty'\n   */\n  emptyLabelText: PropTypes.node,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current value of the rating.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @param {number} value The rating label's value to format.\n   * @returns {string}\n   * @default function defaultLabelText(value) {\n   *   return `${value} Star${value !== 1 ? 's' : ''}`;\n   * }\n   */\n  getLabelText: PropTypes.func,\n  /**\n   * If `true`, only the selected icon will be highlighted.\n   * @default false\n   */\n  highlightSelectedOnly: PropTypes.bool,\n  /**\n   * The icon to display.\n   * @default <Star fontSize=\"inherit\" />\n   */\n  icon: PropTypes.node,\n  /**\n   * The component containing the icon.\n   * @default function IconContainer(props) {\n   *   const { value, ...other } = props;\n   *   return <span {...other} />;\n   * }\n   */\n  IconContainerComponent: PropTypes.elementType,\n  /**\n   * Maximum rating.\n   * @default 5\n   */\n  max: PropTypes.number,\n  /**\n   * The name attribute of the radio `input` elements.\n   * This input `name` should be unique within the page.\n   * Being unique within a form is insufficient since the `name` is used to generated IDs.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number|null} value The new value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback function that is fired when the hover state changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number} value The new value.\n   */\n  onChangeActive: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseMove: PropTypes.func,\n  /**\n   * The minimum increment value change allowed.\n   * @default 1\n   */\n  precision: chainPropTypes(PropTypes.number, props => {\n    if (props.precision < 0.1) {\n      return new Error(['MUI: The prop `precision` should be above 0.1.', 'A value below this limit has an imperceptible impact.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * Removes all hover effects and pointer events.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The rating value.\n   */\n  value: PropTypes.number\n} : void 0;\nexport default Rating;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "PropTypes", "clsx", "clamp", "visuallyHidden", "chainPropTypes", "composeClasses", "useRtl", "capitalize", "useForkRef", "useIsFocusVisible", "useControlled", "unstable_useId", "useId", "Star", "StarBorder", "useDefaultProps", "styled", "slotShouldForwardProp", "ratingClasses", "getRatingUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "getDecimalPrecision", "num", "decimalPart", "toString", "split", "length", "roundValueToPrecision", "value", "precision", "nearest", "Math", "round", "Number", "toFixed", "useUtilityClasses", "ownerState", "classes", "size", "readOnly", "disabled", "emptyValueFocused", "focusVisible", "slots", "root", "concat", "label", "labelEmptyValue", "icon", "iconEmpty", "iconFilled", "iconHover", "iconFocus", "iconActive", "decimal", "RatingRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "display", "position", "fontSize", "typography", "pxToRem", "color", "cursor", "textAlign", "width", "WebkitTapHighlightColor", "opacity", "vars", "palette", "action", "disabledOpacity", "pointerEvents", "outline", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "labelEmptyValueActive", "_ref3", "top", "bottom", "RatingIcon", "_ref4", "transition", "transitions", "create", "duration", "shortest", "transform", "RatingDecimal", "shouldForwardProp", "prop", "_ref5", "IconContainer", "other", "process", "env", "NODE_ENV", "propTypes", "number", "isRequired", "RatingItem", "emptyIcon", "focus", "getLabelText", "highlightSelectedOnly", "hover", "IconContainerComponent", "isActive", "itemValue", "labelProps", "onBlur", "onChange", "onClick", "onFocus", "ratingValue", "ratingValueRounded", "isFilled", "isHovered", "isFocused", "isChecked", "id", "container", "as", "className", "children", "Fragment", "undefined", "htmlFor", "type", "checked", "object", "bool", "node", "func", "elementType", "string", "defaultIcon", "defaultEmptyIcon", "defaultLabelText", "Rating", "forwardRef", "inProps", "ref", "defaultValue", "emptyLabelText", "max", "nameProp", "onChangeActive", "onMouseLeave", "onMouseMove", "valueProp", "valueDerived", "setValueState", "controlled", "default", "valueRounded", "isRtl", "setState", "useState", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "focusVisibleRef", "setFocusVisible", "rootRef", "useRef", "handleRef", "handleMouseMove", "event", "rootNode", "current", "right", "left", "containerWidth", "getBoundingClientRect", "percent", "clientX", "newHover", "prev", "handleMouseLeave", "handleChange", "newValue", "target", "parseFloat", "handleClear", "clientY", "handleFocus", "newFocus", "handleBlur", "setEmptyValueFocused", "role", "Array", "from", "map", "_", "index", "ratingItemProps", "ceil", "items", "$", "indexDecimal", "itemDecimalValue", "style", "overflow", "Error", "join", "oneOfType", "oneOf", "sx", "arrayOf"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/Rating/Rating.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"value\"],\n  _excluded2 = [\"className\", \"defaultValue\", \"disabled\", \"emptyIcon\", \"emptyLabelText\", \"getLabelText\", \"highlightSelectedOnly\", \"icon\", \"IconContainerComponent\", \"max\", \"name\", \"onChange\", \"onChangeActive\", \"onMouseLeave\", \"onMouseMove\", \"precision\", \"readOnly\", \"size\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport clamp from '@mui/utils/clamp';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { capitalize, useForkRef, useIsFocusVisible, useControlled, unstable_useId as useId } from '../utils';\nimport Star from '../internal/svg-icons/Star';\nimport StarBorder from '../internal/svg-icons/StarBorder';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled, { slotShouldForwardProp } from '../styles/styled';\nimport ratingClasses, { getRatingUtilityClass } from './ratingClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction getDecimalPrecision(num) {\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToPrecision(value, precision) {\n  if (value == null) {\n    return value;\n  }\n  const nearest = Math.round(value / precision) * precision;\n  return Number(nearest.toFixed(getDecimalPrecision(precision)));\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    size,\n    readOnly,\n    disabled,\n    emptyValueFocused,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', `size${capitalize(size)}`, disabled && 'disabled', focusVisible && 'focusVisible', readOnly && 'readOnly'],\n    label: ['label', 'pristine'],\n    labelEmptyValue: [emptyValueFocused && 'labelEmptyValueActive'],\n    icon: ['icon'],\n    iconEmpty: ['iconEmpty'],\n    iconFilled: ['iconFilled'],\n    iconHover: ['iconHover'],\n    iconFocus: ['iconFocus'],\n    iconActive: ['iconActive'],\n    decimal: ['decimal'],\n    visuallyHidden: ['visuallyHidden']\n  };\n  return composeClasses(slots, getRatingUtilityClass, classes);\n};\nconst RatingRoot = styled('span', {\n  name: 'MuiRating',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${ratingClasses.visuallyHidden}`]: styles.visuallyHidden\n    }, styles.root, styles[`size${capitalize(ownerState.size)}`], ownerState.readOnly && styles.readOnly];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'inline-flex',\n  // Required to position the pristine input absolutely\n  position: 'relative',\n  fontSize: theme.typography.pxToRem(24),\n  color: '#faaf00',\n  cursor: 'pointer',\n  textAlign: 'left',\n  width: 'min-content',\n  WebkitTapHighlightColor: 'transparent',\n  [`&.${ratingClasses.disabled}`]: {\n    opacity: (theme.vars || theme).palette.action.disabledOpacity,\n    pointerEvents: 'none'\n  },\n  [`&.${ratingClasses.focusVisible} .${ratingClasses.iconActive}`]: {\n    outline: '1px solid #999'\n  },\n  [`& .${ratingClasses.visuallyHidden}`]: visuallyHidden\n}, ownerState.size === 'small' && {\n  fontSize: theme.typography.pxToRem(18)\n}, ownerState.size === 'large' && {\n  fontSize: theme.typography.pxToRem(30)\n}, ownerState.readOnly && {\n  pointerEvents: 'none'\n}));\nconst RatingLabel = styled('label', {\n  name: 'MuiRating',\n  slot: 'Label',\n  overridesResolver: ({\n    ownerState\n  }, styles) => [styles.label, ownerState.emptyValueFocused && styles.labelEmptyValueActive]\n})(({\n  ownerState\n}) => _extends({\n  cursor: 'inherit'\n}, ownerState.emptyValueFocused && {\n  top: 0,\n  bottom: 0,\n  position: 'absolute',\n  outline: '1px solid #999',\n  width: '100%'\n}));\nconst RatingIcon = styled('span', {\n  name: 'MuiRating',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.iconEmpty && styles.iconEmpty, ownerState.iconFilled && styles.iconFilled, ownerState.iconHover && styles.iconHover, ownerState.iconFocus && styles.iconFocus, ownerState.iconActive && styles.iconActive];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  // Fit wrapper to actual icon size.\n  display: 'flex',\n  transition: theme.transitions.create('transform', {\n    duration: theme.transitions.duration.shortest\n  }),\n  // Fix mouseLeave issue.\n  // https://github.com/facebook/react/issues/4492\n  pointerEvents: 'none'\n}, ownerState.iconActive && {\n  transform: 'scale(1.2)'\n}, ownerState.iconEmpty && {\n  color: (theme.vars || theme).palette.action.disabled\n}));\nconst RatingDecimal = styled('span', {\n  name: 'MuiRating',\n  slot: 'Decimal',\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'iconActive',\n  overridesResolver: (props, styles) => {\n    const {\n      iconActive\n    } = props;\n    return [styles.decimal, iconActive && styles.iconActive];\n  }\n})(({\n  iconActive\n}) => _extends({\n  position: 'relative'\n}, iconActive && {\n  transform: 'scale(1.2)'\n}));\nfunction IconContainer(props) {\n  const other = _objectWithoutPropertiesLoose(props, _excluded);\n  return /*#__PURE__*/_jsx(\"span\", _extends({}, other));\n}\nprocess.env.NODE_ENV !== \"production\" ? IconContainer.propTypes = {\n  value: PropTypes.number.isRequired\n} : void 0;\nfunction RatingItem(props) {\n  const {\n    classes,\n    disabled,\n    emptyIcon,\n    focus,\n    getLabelText,\n    highlightSelectedOnly,\n    hover,\n    icon,\n    IconContainerComponent,\n    isActive,\n    itemValue,\n    labelProps,\n    name,\n    onBlur,\n    onChange,\n    onClick,\n    onFocus,\n    readOnly,\n    ownerState,\n    ratingValue,\n    ratingValueRounded\n  } = props;\n  const isFilled = highlightSelectedOnly ? itemValue === ratingValue : itemValue <= ratingValue;\n  const isHovered = itemValue <= hover;\n  const isFocused = itemValue <= focus;\n  const isChecked = itemValue === ratingValueRounded;\n  const id = useId();\n  const container = /*#__PURE__*/_jsx(RatingIcon, {\n    as: IconContainerComponent,\n    value: itemValue,\n    className: clsx(classes.icon, isFilled ? classes.iconFilled : classes.iconEmpty, isHovered && classes.iconHover, isFocused && classes.iconFocus, isActive && classes.iconActive),\n    ownerState: _extends({}, ownerState, {\n      iconEmpty: !isFilled,\n      iconFilled: isFilled,\n      iconHover: isHovered,\n      iconFocus: isFocused,\n      iconActive: isActive\n    }),\n    children: emptyIcon && !isFilled ? emptyIcon : icon\n  });\n  if (readOnly) {\n    return /*#__PURE__*/_jsx(\"span\", _extends({}, labelProps, {\n      children: container\n    }));\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsxs(RatingLabel, _extends({\n      ownerState: _extends({}, ownerState, {\n        emptyValueFocused: undefined\n      }),\n      htmlFor: id\n    }, labelProps, {\n      children: [container, /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: getLabelText(itemValue)\n      })]\n    })), /*#__PURE__*/_jsx(\"input\", {\n      className: classes.visuallyHidden,\n      onFocus: onFocus,\n      onBlur: onBlur,\n      onChange: onChange,\n      onClick: onClick,\n      disabled: disabled,\n      value: itemValue,\n      id: id,\n      type: \"radio\",\n      name: name,\n      checked: isChecked\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RatingItem.propTypes = {\n  classes: PropTypes.object.isRequired,\n  disabled: PropTypes.bool.isRequired,\n  emptyIcon: PropTypes.node,\n  focus: PropTypes.number.isRequired,\n  getLabelText: PropTypes.func.isRequired,\n  highlightSelectedOnly: PropTypes.bool.isRequired,\n  hover: PropTypes.number.isRequired,\n  icon: PropTypes.node,\n  IconContainerComponent: PropTypes.elementType.isRequired,\n  isActive: PropTypes.bool.isRequired,\n  itemValue: PropTypes.number.isRequired,\n  labelProps: PropTypes.object,\n  name: PropTypes.string,\n  onBlur: PropTypes.func.isRequired,\n  onChange: PropTypes.func.isRequired,\n  onClick: PropTypes.func.isRequired,\n  onFocus: PropTypes.func.isRequired,\n  ownerState: PropTypes.object.isRequired,\n  ratingValue: PropTypes.number,\n  ratingValueRounded: PropTypes.number,\n  readOnly: PropTypes.bool.isRequired\n} : void 0;\nconst defaultIcon = /*#__PURE__*/_jsx(Star, {\n  fontSize: \"inherit\"\n});\nconst defaultEmptyIcon = /*#__PURE__*/_jsx(StarBorder, {\n  fontSize: \"inherit\"\n});\nfunction defaultLabelText(value) {\n  return `${value} Star${value !== 1 ? 's' : ''}`;\n}\nconst Rating = /*#__PURE__*/React.forwardRef(function Rating(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiRating',\n    props: inProps\n  });\n  const {\n      className,\n      defaultValue = null,\n      disabled = false,\n      emptyIcon = defaultEmptyIcon,\n      emptyLabelText = 'Empty',\n      getLabelText = defaultLabelText,\n      highlightSelectedOnly = false,\n      icon = defaultIcon,\n      IconContainerComponent = IconContainer,\n      max = 5,\n      name: nameProp,\n      onChange,\n      onChangeActive,\n      onMouseLeave,\n      onMouseMove,\n      precision = 1,\n      readOnly = false,\n      size = 'medium',\n      value: valueProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const name = useId(nameProp);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Rating'\n  });\n  const valueRounded = roundValueToPrecision(valueDerived, precision);\n  const isRtl = useRtl();\n  const [{\n    hover,\n    focus\n  }, setState] = React.useState({\n    hover: -1,\n    focus: -1\n  });\n  let value = valueRounded;\n  if (hover !== -1) {\n    value = hover;\n  }\n  if (focus !== -1) {\n    value = focus;\n  }\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const rootRef = React.useRef();\n  const handleRef = useForkRef(focusVisibleRef, rootRef, ref);\n  const handleMouseMove = event => {\n    if (onMouseMove) {\n      onMouseMove(event);\n    }\n    const rootNode = rootRef.current;\n    const {\n      right,\n      left,\n      width: containerWidth\n    } = rootNode.getBoundingClientRect();\n    let percent;\n    if (isRtl) {\n      percent = (right - event.clientX) / containerWidth;\n    } else {\n      percent = (event.clientX - left) / containerWidth;\n    }\n    let newHover = roundValueToPrecision(max * percent + precision / 2, precision);\n    newHover = clamp(newHover, precision, max);\n    setState(prev => prev.hover === newHover && prev.focus === newHover ? prev : {\n      hover: newHover,\n      focus: newHover\n    });\n    setFocusVisible(false);\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleMouseLeave = event => {\n    if (onMouseLeave) {\n      onMouseLeave(event);\n    }\n    const newHover = -1;\n    setState({\n      hover: newHover,\n      focus: newHover\n    });\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleChange = event => {\n    let newValue = event.target.value === '' ? null : parseFloat(event.target.value);\n\n    // Give mouse priority over keyboard\n    // Fix https://github.com/mui/material-ui/issues/22827\n    if (hover !== -1) {\n      newValue = hover;\n    }\n    setValueState(newValue);\n    if (onChange) {\n      onChange(event, newValue);\n    }\n  };\n  const handleClear = event => {\n    // Ignore keyboard events\n    // https://github.com/facebook/react/issues/7407\n    if (event.clientX === 0 && event.clientY === 0) {\n      return;\n    }\n    setState({\n      hover: -1,\n      focus: -1\n    });\n    setValueState(null);\n    if (onChange && parseFloat(event.target.value) === valueRounded) {\n      onChange(event, null);\n    }\n  };\n  const handleFocus = event => {\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n    }\n    const newFocus = parseFloat(event.target.value);\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const handleBlur = event => {\n    if (hover !== -1) {\n      return;\n    }\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    const newFocus = -1;\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const [emptyValueFocused, setEmptyValueFocused] = React.useState(false);\n  const ownerState = _extends({}, props, {\n    defaultValue,\n    disabled,\n    emptyIcon,\n    emptyLabelText,\n    emptyValueFocused,\n    focusVisible,\n    getLabelText,\n    icon,\n    IconContainerComponent,\n    max,\n    precision,\n    readOnly,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(RatingRoot, _extends({\n    ref: handleRef,\n    onMouseMove: handleMouseMove,\n    onMouseLeave: handleMouseLeave,\n    className: clsx(classes.root, className, readOnly && 'MuiRating-readOnly'),\n    ownerState: ownerState,\n    role: readOnly ? 'img' : null,\n    \"aria-label\": readOnly ? getLabelText(value) : null\n  }, other, {\n    children: [Array.from(new Array(max)).map((_, index) => {\n      const itemValue = index + 1;\n      const ratingItemProps = {\n        classes,\n        disabled,\n        emptyIcon,\n        focus,\n        getLabelText,\n        highlightSelectedOnly,\n        hover,\n        icon,\n        IconContainerComponent,\n        name,\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onClick: handleClear,\n        onFocus: handleFocus,\n        ratingValue: value,\n        ratingValueRounded: valueRounded,\n        readOnly,\n        ownerState\n      };\n      const isActive = itemValue === Math.ceil(value) && (hover !== -1 || focus !== -1);\n      if (precision < 1) {\n        const items = Array.from(new Array(1 / precision));\n        return /*#__PURE__*/_jsx(RatingDecimal, {\n          className: clsx(classes.decimal, isActive && classes.iconActive),\n          ownerState: ownerState,\n          iconActive: isActive,\n          children: items.map(($, indexDecimal) => {\n            const itemDecimalValue = roundValueToPrecision(itemValue - 1 + (indexDecimal + 1) * precision, precision);\n            return /*#__PURE__*/_jsx(RatingItem, _extends({}, ratingItemProps, {\n              // The icon is already displayed as active\n              isActive: false,\n              itemValue: itemDecimalValue,\n              labelProps: {\n                style: items.length - 1 === indexDecimal ? {} : {\n                  width: itemDecimalValue === value ? `${(indexDecimal + 1) * precision * 100}%` : '0%',\n                  overflow: 'hidden',\n                  position: 'absolute'\n                }\n              }\n            }), itemDecimalValue);\n          })\n        }, itemValue);\n      }\n      return /*#__PURE__*/_jsx(RatingItem, _extends({}, ratingItemProps, {\n        isActive: isActive,\n        itemValue: itemValue\n      }), itemValue);\n    }), !readOnly && !disabled && /*#__PURE__*/_jsxs(RatingLabel, {\n      className: clsx(classes.label, classes.labelEmptyValue),\n      ownerState: ownerState,\n      children: [/*#__PURE__*/_jsx(\"input\", {\n        className: classes.visuallyHidden,\n        value: \"\",\n        id: `${name}-empty`,\n        type: \"radio\",\n        name: name,\n        checked: valueRounded == null,\n        onFocus: () => setEmptyValueFocused(true),\n        onBlur: () => setEmptyValueFocused(false),\n        onChange: handleChange\n      }), /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: emptyLabelText\n      })]\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Rating.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default null\n   */\n  defaultValue: PropTypes.number,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The icon to display when empty.\n   * @default <StarBorder fontSize=\"inherit\" />\n   */\n  emptyIcon: PropTypes.node,\n  /**\n   * The label read when the rating input is empty.\n   * @default 'Empty'\n   */\n  emptyLabelText: PropTypes.node,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current value of the rating.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @param {number} value The rating label's value to format.\n   * @returns {string}\n   * @default function defaultLabelText(value) {\n   *   return `${value} Star${value !== 1 ? 's' : ''}`;\n   * }\n   */\n  getLabelText: PropTypes.func,\n  /**\n   * If `true`, only the selected icon will be highlighted.\n   * @default false\n   */\n  highlightSelectedOnly: PropTypes.bool,\n  /**\n   * The icon to display.\n   * @default <Star fontSize=\"inherit\" />\n   */\n  icon: PropTypes.node,\n  /**\n   * The component containing the icon.\n   * @default function IconContainer(props) {\n   *   const { value, ...other } = props;\n   *   return <span {...other} />;\n   * }\n   */\n  IconContainerComponent: PropTypes.elementType,\n  /**\n   * Maximum rating.\n   * @default 5\n   */\n  max: PropTypes.number,\n  /**\n   * The name attribute of the radio `input` elements.\n   * This input `name` should be unique within the page.\n   * Being unique within a form is insufficient since the `name` is used to generated IDs.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number|null} value The new value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback function that is fired when the hover state changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number} value The new value.\n   */\n  onChangeActive: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseMove: PropTypes.func,\n  /**\n   * The minimum increment value change allowed.\n   * @default 1\n   */\n  precision: chainPropTypes(PropTypes.number, props => {\n    if (props.precision < 0.1) {\n      return new Error(['MUI: The prop `precision` should be above 0.1.', 'A value below this limit has an imperceptible impact.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * Removes all hover effects and pointer events.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The rating value.\n   */\n  value: PropTypes.number\n} : void 0;\nexport default Rating;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,OAAO,CAAC;EACzBC,UAAU,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,uBAAuB,EAAE,MAAM,EAAE,wBAAwB,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,gBAAgB,EAAE,cAAc,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;AACxR,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,UAAU,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,cAAc,IAAIC,KAAK,QAAQ,UAAU;AAC5G,OAAOC,IAAI,MAAM,4BAA4B;AAC7C,OAAOC,UAAU,MAAM,kCAAkC;AACzD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,IAAIC,qBAAqB,QAAQ,kBAAkB;AAChE,OAAOC,aAAa,IAAIC,qBAAqB,QAAQ,iBAAiB;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,SAASC,mBAAmBA,CAACC,GAAG,EAAE;EAChC,MAAMC,WAAW,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOF,WAAW,GAAGA,WAAW,CAACG,MAAM,GAAG,CAAC;AAC7C;AACA,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC/C,IAAID,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOA,KAAK;EACd;EACA,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,KAAK,GAAGC,SAAS,CAAC,GAAGA,SAAS;EACzD,OAAOI,MAAM,CAACH,OAAO,CAACI,OAAO,CAACb,mBAAmB,CAACQ,SAAS,CAAC,CAAC,CAAC;AAChE;AACA,MAAMM,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,iBAAiB;IACjBC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,SAAAC,MAAA,CAASzC,UAAU,CAACkC,IAAI,CAAC,GAAIE,QAAQ,IAAI,UAAU,EAAEE,YAAY,IAAI,cAAc,EAAEH,QAAQ,IAAI,UAAU,CAAC;IACzHO,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;IAC5BC,eAAe,EAAE,CAACN,iBAAiB,IAAI,uBAAuB,CAAC;IAC/DO,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,YAAY,CAAC;IAC1BC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,YAAY,CAAC;IAC1BC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBtD,cAAc,EAAE,CAAC,gBAAgB;EACnC,CAAC;EACD,OAAOE,cAAc,CAACyC,KAAK,EAAE3B,qBAAqB,EAAEqB,OAAO,CAAC;AAC9D,CAAC;AACD,MAAMkB,UAAU,GAAG1C,MAAM,CAAC,MAAM,EAAE;EAChC2C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJxB;IACF,CAAC,GAAGuB,KAAK;IACT,OAAO,CAAC;MACN,OAAAd,MAAA,CAAO9B,aAAa,CAACf,cAAc,IAAK4D,MAAM,CAAC5D;IACjD,CAAC,EAAE4D,MAAM,CAAChB,IAAI,EAAEgB,MAAM,QAAAf,MAAA,CAAQzC,UAAU,CAACgC,UAAU,CAACE,IAAI,CAAC,EAAG,EAAEF,UAAU,CAACG,QAAQ,IAAIqB,MAAM,CAACrB,QAAQ,CAAC;EACvG;AACF,CAAC,CAAC,CAACsB,IAAA;EAAA,IAAC;IACFC,KAAK;IACL1B;EACF,CAAC,GAAAyB,IAAA;EAAA,OAAKpE,QAAQ,CAAC;IACbsE,OAAO,EAAE,aAAa;IACtB;IACAC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAEH,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,EAAE,CAAC;IACtCC,KAAK,EAAE,SAAS;IAChBC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE,aAAa;IACpBC,uBAAuB,EAAE,aAAa;IACtC,MAAA3B,MAAA,CAAM9B,aAAa,CAACyB,QAAQ,IAAK;MAC/BiC,OAAO,EAAE,CAACX,KAAK,CAACY,IAAI,IAAIZ,KAAK,EAAEa,OAAO,CAACC,MAAM,CAACC,eAAe;MAC7DC,aAAa,EAAE;IACjB,CAAC;IACD,MAAAjC,MAAA,CAAM9B,aAAa,CAAC2B,YAAY,QAAAG,MAAA,CAAK9B,aAAa,CAACsC,UAAU,IAAK;MAChE0B,OAAO,EAAE;IACX,CAAC;IACD,OAAAlC,MAAA,CAAO9B,aAAa,CAACf,cAAc,IAAKA;EAC1C,CAAC,EAAEoC,UAAU,CAACE,IAAI,KAAK,OAAO,IAAI;IAChC2B,QAAQ,EAAEH,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,EAAE;EACvC,CAAC,EAAE/B,UAAU,CAACE,IAAI,KAAK,OAAO,IAAI;IAChC2B,QAAQ,EAAEH,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,EAAE;EACvC,CAAC,EAAE/B,UAAU,CAACG,QAAQ,IAAI;IACxBuC,aAAa,EAAE;EACjB,CAAC,CAAC;AAAA,EAAC;AACH,MAAME,WAAW,GAAGnE,MAAM,CAAC,OAAO,EAAE;EAClC2C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAAAuB,KAAA,EAEhBrB,MAAM;IAAA,IAFW;MAClBxB;IACF,CAAC,GAAA6C,KAAA;IAAA,OAAa,CAACrB,MAAM,CAACd,KAAK,EAAEV,UAAU,CAACK,iBAAiB,IAAImB,MAAM,CAACsB,qBAAqB,CAAC;EAAA;AAC5F,CAAC,CAAC,CAACC,KAAA;EAAA,IAAC;IACF/C;EACF,CAAC,GAAA+C,KAAA;EAAA,OAAK1F,QAAQ,CAAC;IACb4E,MAAM,EAAE;EACV,CAAC,EAAEjC,UAAU,CAACK,iBAAiB,IAAI;IACjC2C,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTrB,QAAQ,EAAE,UAAU;IACpBe,OAAO,EAAE,gBAAgB;IACzBR,KAAK,EAAE;EACT,CAAC,CAAC;AAAA,EAAC;AACH,MAAMe,UAAU,GAAGzE,MAAM,CAAC,MAAM,EAAE;EAChC2C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJxB;IACF,CAAC,GAAGuB,KAAK;IACT,OAAO,CAACC,MAAM,CAACZ,IAAI,EAAEZ,UAAU,CAACa,SAAS,IAAIW,MAAM,CAACX,SAAS,EAAEb,UAAU,CAACc,UAAU,IAAIU,MAAM,CAACV,UAAU,EAAEd,UAAU,CAACe,SAAS,IAAIS,MAAM,CAACT,SAAS,EAAEf,UAAU,CAACgB,SAAS,IAAIQ,MAAM,CAACR,SAAS,EAAEhB,UAAU,CAACiB,UAAU,IAAIO,MAAM,CAACP,UAAU,CAAC;EAC5O;AACF,CAAC,CAAC,CAACkC,KAAA;EAAA,IAAC;IACFzB,KAAK;IACL1B;EACF,CAAC,GAAAmD,KAAA;EAAA,OAAK9F,QAAQ,CAAC;IACb;IACAsE,OAAO,EAAE,MAAM;IACfyB,UAAU,EAAE1B,KAAK,CAAC2B,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;MAChDC,QAAQ,EAAE7B,KAAK,CAAC2B,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC,CAAC;IACF;IACA;IACAd,aAAa,EAAE;EACjB,CAAC,EAAE1C,UAAU,CAACiB,UAAU,IAAI;IAC1BwC,SAAS,EAAE;EACb,CAAC,EAAEzD,UAAU,CAACa,SAAS,IAAI;IACzBmB,KAAK,EAAE,CAACN,KAAK,CAACY,IAAI,IAAIZ,KAAK,EAAEa,OAAO,CAACC,MAAM,CAACpC;EAC9C,CAAC,CAAC;AAAA,EAAC;AACH,MAAMsD,aAAa,GAAGjF,MAAM,CAAC,MAAM,EAAE;EACnC2C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,SAAS;EACfsC,iBAAiB,EAAEC,IAAI,IAAIlF,qBAAqB,CAACkF,IAAI,CAAC,IAAIA,IAAI,KAAK,YAAY;EAC/EtC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJP;IACF,CAAC,GAAGM,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,OAAO,EAAED,UAAU,IAAIO,MAAM,CAACP,UAAU,CAAC;EAC1D;AACF,CAAC,CAAC,CAAC4C,KAAA;EAAA,IAAC;IACF5C;EACF,CAAC,GAAA4C,KAAA;EAAA,OAAKxG,QAAQ,CAAC;IACbuE,QAAQ,EAAE;EACZ,CAAC,EAAEX,UAAU,IAAI;IACfwC,SAAS,EAAE;EACb,CAAC,CAAC;AAAA,EAAC;AACH,SAASK,aAAaA,CAACvC,KAAK,EAAE;EAC5B,MAAMwC,KAAK,GAAG3G,6BAA6B,CAACmE,KAAK,EAAEjE,SAAS,CAAC;EAC7D,OAAO,aAAawB,IAAI,CAAC,MAAM,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAE0G,KAAK,CAAC,CAAC;AACvD;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,aAAa,CAACK,SAAS,GAAG;EAChE3E,KAAK,EAAE/B,SAAS,CAAC2G,MAAM,CAACC;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAASC,UAAUA,CAAC/C,KAAK,EAAE;EACzB,MAAM;IACJtB,OAAO;IACPG,QAAQ;IACRmE,SAAS;IACTC,KAAK;IACLC,YAAY;IACZC,qBAAqB;IACrBC,KAAK;IACL/D,IAAI;IACJgE,sBAAsB;IACtBC,QAAQ;IACRC,SAAS;IACTC,UAAU;IACV3D,IAAI;IACJ4D,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,OAAO;IACPhF,QAAQ;IACRH,UAAU;IACVoF,WAAW;IACXC;EACF,CAAC,GAAG9D,KAAK;EACT,MAAM+D,QAAQ,GAAGZ,qBAAqB,GAAGI,SAAS,KAAKM,WAAW,GAAGN,SAAS,IAAIM,WAAW;EAC7F,MAAMG,SAAS,GAAGT,SAAS,IAAIH,KAAK;EACpC,MAAMa,SAAS,GAAGV,SAAS,IAAIN,KAAK;EACpC,MAAMiB,SAAS,GAAGX,SAAS,KAAKO,kBAAkB;EAClD,MAAMK,EAAE,GAAGrH,KAAK,CAAC,CAAC;EAClB,MAAMsH,SAAS,GAAG,aAAa7G,IAAI,CAACoE,UAAU,EAAE;IAC9C0C,EAAE,EAAEhB,sBAAsB;IAC1BpF,KAAK,EAAEsF,SAAS;IAChBe,SAAS,EAAEnI,IAAI,CAACuC,OAAO,CAACW,IAAI,EAAE0E,QAAQ,GAAGrF,OAAO,CAACa,UAAU,GAAGb,OAAO,CAACY,SAAS,EAAE0E,SAAS,IAAItF,OAAO,CAACc,SAAS,EAAEyE,SAAS,IAAIvF,OAAO,CAACe,SAAS,EAAE6D,QAAQ,IAAI5E,OAAO,CAACgB,UAAU,CAAC;IAChLjB,UAAU,EAAE3C,QAAQ,CAAC,CAAC,CAAC,EAAE2C,UAAU,EAAE;MACnCa,SAAS,EAAE,CAACyE,QAAQ;MACpBxE,UAAU,EAAEwE,QAAQ;MACpBvE,SAAS,EAAEwE,SAAS;MACpBvE,SAAS,EAAEwE,SAAS;MACpBvE,UAAU,EAAE4D;IACd,CAAC,CAAC;IACFiB,QAAQ,EAAEvB,SAAS,IAAI,CAACe,QAAQ,GAAGf,SAAS,GAAG3D;EACjD,CAAC,CAAC;EACF,IAAIT,QAAQ,EAAE;IACZ,OAAO,aAAarB,IAAI,CAAC,MAAM,EAAEzB,QAAQ,CAAC,CAAC,CAAC,EAAE0H,UAAU,EAAE;MACxDe,QAAQ,EAAEH;IACZ,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAa3G,KAAK,CAACxB,KAAK,CAACuI,QAAQ,EAAE;IACxCD,QAAQ,EAAE,CAAC,aAAa9G,KAAK,CAAC4D,WAAW,EAAEvF,QAAQ,CAAC;MAClD2C,UAAU,EAAE3C,QAAQ,CAAC,CAAC,CAAC,EAAE2C,UAAU,EAAE;QACnCK,iBAAiB,EAAE2F;MACrB,CAAC,CAAC;MACFC,OAAO,EAAEP;IACX,CAAC,EAAEX,UAAU,EAAE;MACbe,QAAQ,EAAE,CAACH,SAAS,EAAE,aAAa7G,IAAI,CAAC,MAAM,EAAE;QAC9C+G,SAAS,EAAE5F,OAAO,CAACrC,cAAc;QACjCkI,QAAQ,EAAErB,YAAY,CAACK,SAAS;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,EAAE,aAAahG,IAAI,CAAC,OAAO,EAAE;MAC9B+G,SAAS,EAAE5F,OAAO,CAACrC,cAAc;MACjCuH,OAAO,EAAEA,OAAO;MAChBH,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA,QAAQ;MAClBC,OAAO,EAAEA,OAAO;MAChB9E,QAAQ,EAAEA,QAAQ;MAClBZ,KAAK,EAAEsF,SAAS;MAChBY,EAAE,EAAEA,EAAE;MACNQ,IAAI,EAAE,OAAO;MACb9E,IAAI,EAAEA,IAAI;MACV+E,OAAO,EAAEV;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACAzB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGI,UAAU,CAACH,SAAS,GAAG;EAC7DlE,OAAO,EAAExC,SAAS,CAAC2I,MAAM,CAAC/B,UAAU;EACpCjE,QAAQ,EAAE3C,SAAS,CAAC4I,IAAI,CAAChC,UAAU;EACnCE,SAAS,EAAE9G,SAAS,CAAC6I,IAAI;EACzB9B,KAAK,EAAE/G,SAAS,CAAC2G,MAAM,CAACC,UAAU;EAClCI,YAAY,EAAEhH,SAAS,CAAC8I,IAAI,CAAClC,UAAU;EACvCK,qBAAqB,EAAEjH,SAAS,CAAC4I,IAAI,CAAChC,UAAU;EAChDM,KAAK,EAAElH,SAAS,CAAC2G,MAAM,CAACC,UAAU;EAClCzD,IAAI,EAAEnD,SAAS,CAAC6I,IAAI;EACpB1B,sBAAsB,EAAEnH,SAAS,CAAC+I,WAAW,CAACnC,UAAU;EACxDQ,QAAQ,EAAEpH,SAAS,CAAC4I,IAAI,CAAChC,UAAU;EACnCS,SAAS,EAAErH,SAAS,CAAC2G,MAAM,CAACC,UAAU;EACtCU,UAAU,EAAEtH,SAAS,CAAC2I,MAAM;EAC5BhF,IAAI,EAAE3D,SAAS,CAACgJ,MAAM;EACtBzB,MAAM,EAAEvH,SAAS,CAAC8I,IAAI,CAAClC,UAAU;EACjCY,QAAQ,EAAExH,SAAS,CAAC8I,IAAI,CAAClC,UAAU;EACnCa,OAAO,EAAEzH,SAAS,CAAC8I,IAAI,CAAClC,UAAU;EAClCc,OAAO,EAAE1H,SAAS,CAAC8I,IAAI,CAAClC,UAAU;EAClCrE,UAAU,EAAEvC,SAAS,CAAC2I,MAAM,CAAC/B,UAAU;EACvCe,WAAW,EAAE3H,SAAS,CAAC2G,MAAM;EAC7BiB,kBAAkB,EAAE5H,SAAS,CAAC2G,MAAM;EACpCjE,QAAQ,EAAE1C,SAAS,CAAC4I,IAAI,CAAChC;AAC3B,CAAC,GAAG,KAAK,CAAC;AACV,MAAMqC,WAAW,GAAG,aAAa5H,IAAI,CAACR,IAAI,EAAE;EAC1CuD,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAM8E,gBAAgB,GAAG,aAAa7H,IAAI,CAACP,UAAU,EAAE;EACrDsD,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,SAAS+E,gBAAgBA,CAACpH,KAAK,EAAE;EAC/B,UAAAiB,MAAA,CAAUjB,KAAK,WAAAiB,MAAA,CAAQjB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;AAC/C;AACA,MAAMqH,MAAM,GAAG,aAAarJ,KAAK,CAACsJ,UAAU,CAAC,SAASD,MAAMA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAMzF,KAAK,GAAG/C,eAAe,CAAC;IAC5B4C,IAAI,EAAE,WAAW;IACjBG,KAAK,EAAEwF;EACT,CAAC,CAAC;EACF,MAAM;MACFlB,SAAS;MACToB,YAAY,GAAG,IAAI;MACnB7G,QAAQ,GAAG,KAAK;MAChBmE,SAAS,GAAGoC,gBAAgB;MAC5BO,cAAc,GAAG,OAAO;MACxBzC,YAAY,GAAGmC,gBAAgB;MAC/BlC,qBAAqB,GAAG,KAAK;MAC7B9D,IAAI,GAAG8F,WAAW;MAClB9B,sBAAsB,GAAGd,aAAa;MACtCqD,GAAG,GAAG,CAAC;MACP/F,IAAI,EAAEgG,QAAQ;MACdnC,QAAQ;MACRoC,cAAc;MACdC,YAAY;MACZC,WAAW;MACX9H,SAAS,GAAG,CAAC;MACbU,QAAQ,GAAG,KAAK;MAChBD,IAAI,GAAG,QAAQ;MACfV,KAAK,EAAEgI;IACT,CAAC,GAAGjG,KAAK;IACTwC,KAAK,GAAG3G,6BAA6B,CAACmE,KAAK,EAAEhE,UAAU,CAAC;EAC1D,MAAM6D,IAAI,GAAG/C,KAAK,CAAC+I,QAAQ,CAAC;EAC5B,MAAM,CAACK,YAAY,EAAEC,aAAa,CAAC,GAAGvJ,aAAa,CAAC;IAClDwJ,UAAU,EAAEH,SAAS;IACrBI,OAAO,EAAEX,YAAY;IACrB7F,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMyG,YAAY,GAAGtI,qBAAqB,CAACkI,YAAY,EAAEhI,SAAS,CAAC;EACnE,MAAMqI,KAAK,GAAG/J,MAAM,CAAC,CAAC;EACtB,MAAM,CAAC;IACL4G,KAAK;IACLH;EACF,CAAC,EAAEuD,QAAQ,CAAC,GAAGvK,KAAK,CAACwK,QAAQ,CAAC;IAC5BrD,KAAK,EAAE,CAAC,CAAC;IACTH,KAAK,EAAE,CAAC;EACV,CAAC,CAAC;EACF,IAAIhF,KAAK,GAAGqI,YAAY;EACxB,IAAIlD,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBnF,KAAK,GAAGmF,KAAK;EACf;EACA,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBhF,KAAK,GAAGgF,KAAK;EACf;EACA,MAAM;IACJyD,iBAAiB;IACjBjD,MAAM,EAAEkD,iBAAiB;IACzB/C,OAAO,EAAEgD,kBAAkB;IAC3BnB,GAAG,EAAEoB;EACP,CAAC,GAAGlK,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACoC,YAAY,EAAE+H,eAAe,CAAC,GAAG7K,KAAK,CAACwK,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMM,OAAO,GAAG9K,KAAK,CAAC+K,MAAM,CAAC,CAAC;EAC9B,MAAMC,SAAS,GAAGvK,UAAU,CAACmK,eAAe,EAAEE,OAAO,EAAEtB,GAAG,CAAC;EAC3D,MAAMyB,eAAe,GAAGC,KAAK,IAAI;IAC/B,IAAInB,WAAW,EAAE;MACfA,WAAW,CAACmB,KAAK,CAAC;IACpB;IACA,MAAMC,QAAQ,GAAGL,OAAO,CAACM,OAAO;IAChC,MAAM;MACJC,KAAK;MACLC,IAAI;MACJ3G,KAAK,EAAE4G;IACT,CAAC,GAAGJ,QAAQ,CAACK,qBAAqB,CAAC,CAAC;IACpC,IAAIC,OAAO;IACX,IAAInB,KAAK,EAAE;MACTmB,OAAO,GAAG,CAACJ,KAAK,GAAGH,KAAK,CAACQ,OAAO,IAAIH,cAAc;IACpD,CAAC,MAAM;MACLE,OAAO,GAAG,CAACP,KAAK,CAACQ,OAAO,GAAGJ,IAAI,IAAIC,cAAc;IACnD;IACA,IAAII,QAAQ,GAAG5J,qBAAqB,CAAC4H,GAAG,GAAG8B,OAAO,GAAGxJ,SAAS,GAAG,CAAC,EAAEA,SAAS,CAAC;IAC9E0J,QAAQ,GAAGxL,KAAK,CAACwL,QAAQ,EAAE1J,SAAS,EAAE0H,GAAG,CAAC;IAC1CY,QAAQ,CAACqB,IAAI,IAAIA,IAAI,CAACzE,KAAK,KAAKwE,QAAQ,IAAIC,IAAI,CAAC5E,KAAK,KAAK2E,QAAQ,GAAGC,IAAI,GAAG;MAC3EzE,KAAK,EAAEwE,QAAQ;MACf3E,KAAK,EAAE2E;IACT,CAAC,CAAC;IACFd,eAAe,CAAC,KAAK,CAAC;IACtB,IAAIhB,cAAc,IAAI1C,KAAK,KAAKwE,QAAQ,EAAE;MACxC9B,cAAc,CAACqB,KAAK,EAAES,QAAQ,CAAC;IACjC;EACF,CAAC;EACD,MAAME,gBAAgB,GAAGX,KAAK,IAAI;IAChC,IAAIpB,YAAY,EAAE;MAChBA,YAAY,CAACoB,KAAK,CAAC;IACrB;IACA,MAAMS,QAAQ,GAAG,CAAC,CAAC;IACnBpB,QAAQ,CAAC;MACPpD,KAAK,EAAEwE,QAAQ;MACf3E,KAAK,EAAE2E;IACT,CAAC,CAAC;IACF,IAAI9B,cAAc,IAAI1C,KAAK,KAAKwE,QAAQ,EAAE;MACxC9B,cAAc,CAACqB,KAAK,EAAES,QAAQ,CAAC;IACjC;EACF,CAAC;EACD,MAAMG,YAAY,GAAGZ,KAAK,IAAI;IAC5B,IAAIa,QAAQ,GAAGb,KAAK,CAACc,MAAM,CAAChK,KAAK,KAAK,EAAE,GAAG,IAAI,GAAGiK,UAAU,CAACf,KAAK,CAACc,MAAM,CAAChK,KAAK,CAAC;;IAEhF;IACA;IACA,IAAImF,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB4E,QAAQ,GAAG5E,KAAK;IAClB;IACA+C,aAAa,CAAC6B,QAAQ,CAAC;IACvB,IAAItE,QAAQ,EAAE;MACZA,QAAQ,CAACyD,KAAK,EAAEa,QAAQ,CAAC;IAC3B;EACF,CAAC;EACD,MAAMG,WAAW,GAAGhB,KAAK,IAAI;IAC3B;IACA;IACA,IAAIA,KAAK,CAACQ,OAAO,KAAK,CAAC,IAAIR,KAAK,CAACiB,OAAO,KAAK,CAAC,EAAE;MAC9C;IACF;IACA5B,QAAQ,CAAC;MACPpD,KAAK,EAAE,CAAC,CAAC;MACTH,KAAK,EAAE,CAAC;IACV,CAAC,CAAC;IACFkD,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIzC,QAAQ,IAAIwE,UAAU,CAACf,KAAK,CAACc,MAAM,CAAChK,KAAK,CAAC,KAAKqI,YAAY,EAAE;MAC/D5C,QAAQ,CAACyD,KAAK,EAAE,IAAI,CAAC;IACvB;EACF,CAAC;EACD,MAAMkB,WAAW,GAAGlB,KAAK,IAAI;IAC3BP,kBAAkB,CAACO,KAAK,CAAC;IACzB,IAAIT,iBAAiB,CAACW,OAAO,KAAK,IAAI,EAAE;MACtCP,eAAe,CAAC,IAAI,CAAC;IACvB;IACA,MAAMwB,QAAQ,GAAGJ,UAAU,CAACf,KAAK,CAACc,MAAM,CAAChK,KAAK,CAAC;IAC/CuI,QAAQ,CAACqB,IAAI,KAAK;MAChBzE,KAAK,EAAEyE,IAAI,CAACzE,KAAK;MACjBH,KAAK,EAAEqF;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAMC,UAAU,GAAGpB,KAAK,IAAI;IAC1B,IAAI/D,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB;IACF;IACAuD,iBAAiB,CAACQ,KAAK,CAAC;IACxB,IAAIT,iBAAiB,CAACW,OAAO,KAAK,KAAK,EAAE;MACvCP,eAAe,CAAC,KAAK,CAAC;IACxB;IACA,MAAMwB,QAAQ,GAAG,CAAC,CAAC;IACnB9B,QAAQ,CAACqB,IAAI,KAAK;MAChBzE,KAAK,EAAEyE,IAAI,CAACzE,KAAK;MACjBH,KAAK,EAAEqF;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAM,CAACxJ,iBAAiB,EAAE0J,oBAAoB,CAAC,GAAGvM,KAAK,CAACwK,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAMhI,UAAU,GAAG3C,QAAQ,CAAC,CAAC,CAAC,EAAEkE,KAAK,EAAE;IACrC0F,YAAY;IACZ7G,QAAQ;IACRmE,SAAS;IACT2C,cAAc;IACd7G,iBAAiB;IACjBC,YAAY;IACZmE,YAAY;IACZ7D,IAAI;IACJgE,sBAAsB;IACtBuC,GAAG;IACH1H,SAAS;IACTU,QAAQ;IACRD;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAahB,KAAK,CAACmC,UAAU,EAAE9D,QAAQ,CAAC;IAC7C2J,GAAG,EAAEwB,SAAS;IACdjB,WAAW,EAAEkB,eAAe;IAC5BnB,YAAY,EAAE+B,gBAAgB;IAC9BxD,SAAS,EAAEnI,IAAI,CAACuC,OAAO,CAACO,IAAI,EAAEqF,SAAS,EAAE1F,QAAQ,IAAI,oBAAoB,CAAC;IAC1EH,UAAU,EAAEA,UAAU;IACtBgK,IAAI,EAAE7J,QAAQ,GAAG,KAAK,GAAG,IAAI;IAC7B,YAAY,EAAEA,QAAQ,GAAGsE,YAAY,CAACjF,KAAK,CAAC,GAAG;EACjD,CAAC,EAAEuE,KAAK,EAAE;IACR+B,QAAQ,EAAE,CAACmE,KAAK,CAACC,IAAI,CAAC,IAAID,KAAK,CAAC9C,GAAG,CAAC,CAAC,CAACgD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;MACtD,MAAMvF,SAAS,GAAGuF,KAAK,GAAG,CAAC;MAC3B,MAAMC,eAAe,GAAG;QACtBrK,OAAO;QACPG,QAAQ;QACRmE,SAAS;QACTC,KAAK;QACLC,YAAY;QACZC,qBAAqB;QACrBC,KAAK;QACL/D,IAAI;QACJgE,sBAAsB;QACtBxD,IAAI;QACJ4D,MAAM,EAAE8E,UAAU;QAClB7E,QAAQ,EAAEqE,YAAY;QACtBpE,OAAO,EAAEwE,WAAW;QACpBvE,OAAO,EAAEyE,WAAW;QACpBxE,WAAW,EAAE5F,KAAK;QAClB6F,kBAAkB,EAAEwC,YAAY;QAChC1H,QAAQ;QACRH;MACF,CAAC;MACD,MAAM6E,QAAQ,GAAGC,SAAS,KAAKnF,IAAI,CAAC4K,IAAI,CAAC/K,KAAK,CAAC,KAAKmF,KAAK,KAAK,CAAC,CAAC,IAAIH,KAAK,KAAK,CAAC,CAAC,CAAC;MACjF,IAAI/E,SAAS,GAAG,CAAC,EAAE;QACjB,MAAM+K,KAAK,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAID,KAAK,CAAC,CAAC,GAAGxK,SAAS,CAAC,CAAC;QAClD,OAAO,aAAaX,IAAI,CAAC4E,aAAa,EAAE;UACtCmC,SAAS,EAAEnI,IAAI,CAACuC,OAAO,CAACiB,OAAO,EAAE2D,QAAQ,IAAI5E,OAAO,CAACgB,UAAU,CAAC;UAChEjB,UAAU,EAAEA,UAAU;UACtBiB,UAAU,EAAE4D,QAAQ;UACpBiB,QAAQ,EAAE0E,KAAK,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,YAAY,KAAK;YACvC,MAAMC,gBAAgB,GAAGpL,qBAAqB,CAACuF,SAAS,GAAG,CAAC,GAAG,CAAC4F,YAAY,GAAG,CAAC,IAAIjL,SAAS,EAAEA,SAAS,CAAC;YACzG,OAAO,aAAaX,IAAI,CAACwF,UAAU,EAAEjH,QAAQ,CAAC,CAAC,CAAC,EAAEiN,eAAe,EAAE;cACjE;cACAzF,QAAQ,EAAE,KAAK;cACfC,SAAS,EAAE6F,gBAAgB;cAC3B5F,UAAU,EAAE;gBACV6F,KAAK,EAAEJ,KAAK,CAAClL,MAAM,GAAG,CAAC,KAAKoL,YAAY,GAAG,CAAC,CAAC,GAAG;kBAC9CvI,KAAK,EAAEwI,gBAAgB,KAAKnL,KAAK,MAAAiB,MAAA,CAAM,CAACiK,YAAY,GAAG,CAAC,IAAIjL,SAAS,GAAG,GAAG,SAAM,IAAI;kBACrFoL,QAAQ,EAAE,QAAQ;kBAClBjJ,QAAQ,EAAE;gBACZ;cACF;YACF,CAAC,CAAC,EAAE+I,gBAAgB,CAAC;UACvB,CAAC;QACH,CAAC,EAAE7F,SAAS,CAAC;MACf;MACA,OAAO,aAAahG,IAAI,CAACwF,UAAU,EAAEjH,QAAQ,CAAC,CAAC,CAAC,EAAEiN,eAAe,EAAE;QACjEzF,QAAQ,EAAEA,QAAQ;QAClBC,SAAS,EAAEA;MACb,CAAC,CAAC,EAAEA,SAAS,CAAC;IAChB,CAAC,CAAC,EAAE,CAAC3E,QAAQ,IAAI,CAACC,QAAQ,IAAI,aAAapB,KAAK,CAAC4D,WAAW,EAAE;MAC5DiD,SAAS,EAAEnI,IAAI,CAACuC,OAAO,CAACS,KAAK,EAAET,OAAO,CAACU,eAAe,CAAC;MACvDX,UAAU,EAAEA,UAAU;MACtB8F,QAAQ,EAAE,CAAC,aAAahH,IAAI,CAAC,OAAO,EAAE;QACpC+G,SAAS,EAAE5F,OAAO,CAACrC,cAAc;QACjC4B,KAAK,EAAE,EAAE;QACTkG,EAAE,KAAAjF,MAAA,CAAKW,IAAI,WAAQ;QACnB8E,IAAI,EAAE,OAAO;QACb9E,IAAI,EAAEA,IAAI;QACV+E,OAAO,EAAE0B,YAAY,IAAI,IAAI;QAC7B1C,OAAO,EAAEA,CAAA,KAAM4E,oBAAoB,CAAC,IAAI,CAAC;QACzC/E,MAAM,EAAEA,CAAA,KAAM+E,oBAAoB,CAAC,KAAK,CAAC;QACzC9E,QAAQ,EAAEqE;MACZ,CAAC,CAAC,EAAE,aAAaxK,IAAI,CAAC,MAAM,EAAE;QAC5B+G,SAAS,EAAE5F,OAAO,CAACrC,cAAc;QACjCkI,QAAQ,EAAEoB;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFlD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG2C,MAAM,CAAC1C,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACElE,OAAO,EAAExC,SAAS,CAAC2I,MAAM;EACzB;AACF;AACA;EACEP,SAAS,EAAEpI,SAAS,CAACgJ,MAAM;EAC3B;AACF;AACA;AACA;EACEQ,YAAY,EAAExJ,SAAS,CAAC2G,MAAM;EAC9B;AACF;AACA;AACA;EACEhE,QAAQ,EAAE3C,SAAS,CAAC4I,IAAI;EACxB;AACF;AACA;AACA;EACE9B,SAAS,EAAE9G,SAAS,CAAC6I,IAAI;EACzB;AACF;AACA;AACA;EACEY,cAAc,EAAEzJ,SAAS,CAAC6I,IAAI;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE7B,YAAY,EAAEhH,SAAS,CAAC8I,IAAI;EAC5B;AACF;AACA;AACA;EACE7B,qBAAqB,EAAEjH,SAAS,CAAC4I,IAAI;EACrC;AACF;AACA;AACA;EACEzF,IAAI,EAAEnD,SAAS,CAAC6I,IAAI;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;EACE1B,sBAAsB,EAAEnH,SAAS,CAAC+I,WAAW;EAC7C;AACF;AACA;AACA;EACEW,GAAG,EAAE1J,SAAS,CAAC2G,MAAM;EACrB;AACF;AACA;AACA;AACA;EACEhD,IAAI,EAAE3D,SAAS,CAACgJ,MAAM;EACtB;AACF;AACA;AACA;AACA;EACExB,QAAQ,EAAExH,SAAS,CAAC8I,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEc,cAAc,EAAE5J,SAAS,CAAC8I,IAAI;EAC9B;AACF;AACA;EACEe,YAAY,EAAE7J,SAAS,CAAC8I,IAAI;EAC5B;AACF;AACA;EACEgB,WAAW,EAAE9J,SAAS,CAAC8I,IAAI;EAC3B;AACF;AACA;AACA;EACE9G,SAAS,EAAE5B,cAAc,CAACJ,SAAS,CAAC2G,MAAM,EAAE7C,KAAK,IAAI;IACnD,IAAIA,KAAK,CAAC9B,SAAS,GAAG,GAAG,EAAE;MACzB,OAAO,IAAIqL,KAAK,CAAC,CAAC,gDAAgD,EAAE,uDAAuD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1I;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE5K,QAAQ,EAAE1C,SAAS,CAAC4I,IAAI;EACxB;AACF;AACA;AACA;EACEnG,IAAI,EAAEzC,SAAS,CAAC,sCAAsCuN,SAAS,CAAC,CAACvN,SAAS,CAACwN,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAExN,SAAS,CAACgJ,MAAM,CAAC,CAAC;EAClI;AACF;AACA;EACEyE,EAAE,EAAEzN,SAAS,CAACuN,SAAS,CAAC,CAACvN,SAAS,CAAC0N,OAAO,CAAC1N,SAAS,CAACuN,SAAS,CAAC,CAACvN,SAAS,CAAC8I,IAAI,EAAE9I,SAAS,CAAC2I,MAAM,EAAE3I,SAAS,CAAC4I,IAAI,CAAC,CAAC,CAAC,EAAE5I,SAAS,CAAC8I,IAAI,EAAE9I,SAAS,CAAC2I,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACE5G,KAAK,EAAE/B,SAAS,CAAC2G;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAeyC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}