{"ast": null, "code": "import responsivePropType from './responsivePropType';\nimport style from './style';\nimport compose from './compose';\nimport { createUnaryUnit, getValue } from './spacing';\nimport { handleBreakpoints } from './breakpoints';\nexport function borderTransform(value) {\n  if (typeof value !== 'number') {\n    return value;\n  }\n  return \"\".concat(value, \"px solid\");\n}\nfunction createBorderStyle(prop, transform) {\n  return style({\n    prop,\n    themeKey: 'borders',\n    transform\n  });\n}\nexport const border = createBorderStyle('border', borderTransform);\nexport const borderTop = createBorderStyle('borderTop', borderTransform);\nexport const borderRight = createBorderStyle('borderRight', borderTransform);\nexport const borderBottom = createBorderStyle('borderBottom', borderTransform);\nexport const borderLeft = createBorderStyle('borderLeft', borderTransform);\nexport const borderColor = createBorderStyle('borderColor');\nexport const borderTopColor = createBorderStyle('borderTopColor');\nexport const borderRightColor = createBorderStyle('borderRightColor');\nexport const borderBottomColor = createBorderStyle('borderBottomColor');\nexport const borderLeftColor = createBorderStyle('borderLeftColor');\nexport const outline = createBorderStyle('outline', borderTransform);\nexport const outlineColor = createBorderStyle('outlineColor');\n\n// false positive\n// eslint-disable-next-line react/function-component-definition\nexport const borderRadius = props => {\n  if (props.borderRadius !== undefined && props.borderRadius !== null) {\n    const transformer = createUnaryUnit(props.theme, 'shape.borderRadius', 4, 'borderRadius');\n    const styleFromPropValue = propValue => ({\n      borderRadius: getValue(transformer, propValue)\n    });\n    return handleBreakpoints(props, props.borderRadius, styleFromPropValue);\n  }\n  return null;\n};\nborderRadius.propTypes = process.env.NODE_ENV !== 'production' ? {\n  borderRadius: responsivePropType\n} : {};\nborderRadius.filterProps = ['borderRadius'];\nconst borders = compose(border, borderTop, borderRight, borderBottom, borderLeft, borderColor, borderTopColor, borderRightColor, borderBottomColor, borderLeftColor, borderRadius, outline, outlineColor);\nexport default borders;", "map": {"version": 3, "names": ["responsivePropType", "style", "compose", "createUnaryUnit", "getValue", "handleBreakpoints", "borderTransform", "value", "concat", "createBorderStyle", "prop", "transform", "<PERSON><PERSON><PERSON>", "border", "borderTop", "borderRight", "borderBottom", "borderLeft", "borderColor", "borderTopColor", "borderRightColor", "borderBottomColor", "borderLeftColor", "outline", "outlineColor", "borderRadius", "props", "undefined", "transformer", "theme", "styleFromPropValue", "propValue", "propTypes", "process", "env", "NODE_ENV", "filterProps", "borders"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/system/esm/borders.js"], "sourcesContent": ["import responsivePropType from './responsivePropType';\nimport style from './style';\nimport compose from './compose';\nimport { createUnaryUnit, getValue } from './spacing';\nimport { handleBreakpoints } from './breakpoints';\nexport function borderTransform(value) {\n  if (typeof value !== 'number') {\n    return value;\n  }\n  return `${value}px solid`;\n}\nfunction createBorderStyle(prop, transform) {\n  return style({\n    prop,\n    themeKey: 'borders',\n    transform\n  });\n}\nexport const border = createBorderStyle('border', borderTransform);\nexport const borderTop = createBorderStyle('borderTop', borderTransform);\nexport const borderRight = createBorderStyle('borderRight', borderTransform);\nexport const borderBottom = createBorderStyle('borderBottom', borderTransform);\nexport const borderLeft = createBorderStyle('borderLeft', borderTransform);\nexport const borderColor = createBorderStyle('borderColor');\nexport const borderTopColor = createBorderStyle('borderTopColor');\nexport const borderRightColor = createBorderStyle('borderRightColor');\nexport const borderBottomColor = createBorderStyle('borderBottomColor');\nexport const borderLeftColor = createBorderStyle('borderLeftColor');\nexport const outline = createBorderStyle('outline', borderTransform);\nexport const outlineColor = createBorderStyle('outlineColor');\n\n// false positive\n// eslint-disable-next-line react/function-component-definition\nexport const borderRadius = props => {\n  if (props.borderRadius !== undefined && props.borderRadius !== null) {\n    const transformer = createUnaryUnit(props.theme, 'shape.borderRadius', 4, 'borderRadius');\n    const styleFromPropValue = propValue => ({\n      borderRadius: getValue(transformer, propValue)\n    });\n    return handleBreakpoints(props, props.borderRadius, styleFromPropValue);\n  }\n  return null;\n};\nborderRadius.propTypes = process.env.NODE_ENV !== 'production' ? {\n  borderRadius: responsivePropType\n} : {};\nborderRadius.filterProps = ['borderRadius'];\nconst borders = compose(border, borderTop, borderRight, borderBottom, borderLeft, borderColor, borderTopColor, borderRightColor, borderBottomColor, borderLeftColor, borderRadius, outline, outlineColor);\nexport default borders;"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,eAAe,EAAEC,QAAQ,QAAQ,WAAW;AACrD,SAASC,iBAAiB,QAAQ,eAAe;AACjD,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EACA,UAAAC,MAAA,CAAUD,KAAK;AACjB;AACA,SAASE,iBAAiBA,CAACC,IAAI,EAAEC,SAAS,EAAE;EAC1C,OAAOV,KAAK,CAAC;IACXS,IAAI;IACJE,QAAQ,EAAE,SAAS;IACnBD;EACF,CAAC,CAAC;AACJ;AACA,OAAO,MAAME,MAAM,GAAGJ,iBAAiB,CAAC,QAAQ,EAAEH,eAAe,CAAC;AAClE,OAAO,MAAMQ,SAAS,GAAGL,iBAAiB,CAAC,WAAW,EAAEH,eAAe,CAAC;AACxE,OAAO,MAAMS,WAAW,GAAGN,iBAAiB,CAAC,aAAa,EAAEH,eAAe,CAAC;AAC5E,OAAO,MAAMU,YAAY,GAAGP,iBAAiB,CAAC,cAAc,EAAEH,eAAe,CAAC;AAC9E,OAAO,MAAMW,UAAU,GAAGR,iBAAiB,CAAC,YAAY,EAAEH,eAAe,CAAC;AAC1E,OAAO,MAAMY,WAAW,GAAGT,iBAAiB,CAAC,aAAa,CAAC;AAC3D,OAAO,MAAMU,cAAc,GAAGV,iBAAiB,CAAC,gBAAgB,CAAC;AACjE,OAAO,MAAMW,gBAAgB,GAAGX,iBAAiB,CAAC,kBAAkB,CAAC;AACrE,OAAO,MAAMY,iBAAiB,GAAGZ,iBAAiB,CAAC,mBAAmB,CAAC;AACvE,OAAO,MAAMa,eAAe,GAAGb,iBAAiB,CAAC,iBAAiB,CAAC;AACnE,OAAO,MAAMc,OAAO,GAAGd,iBAAiB,CAAC,SAAS,EAAEH,eAAe,CAAC;AACpE,OAAO,MAAMkB,YAAY,GAAGf,iBAAiB,CAAC,cAAc,CAAC;;AAE7D;AACA;AACA,OAAO,MAAMgB,YAAY,GAAGC,KAAK,IAAI;EACnC,IAAIA,KAAK,CAACD,YAAY,KAAKE,SAAS,IAAID,KAAK,CAACD,YAAY,KAAK,IAAI,EAAE;IACnE,MAAMG,WAAW,GAAGzB,eAAe,CAACuB,KAAK,CAACG,KAAK,EAAE,oBAAoB,EAAE,CAAC,EAAE,cAAc,CAAC;IACzF,MAAMC,kBAAkB,GAAGC,SAAS,KAAK;MACvCN,YAAY,EAAErB,QAAQ,CAACwB,WAAW,EAAEG,SAAS;IAC/C,CAAC,CAAC;IACF,OAAO1B,iBAAiB,CAACqB,KAAK,EAAEA,KAAK,CAACD,YAAY,EAAEK,kBAAkB,CAAC;EACzE;EACA,OAAO,IAAI;AACb,CAAC;AACDL,YAAY,CAACO,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EAC/DV,YAAY,EAAEzB;AAChB,CAAC,GAAG,CAAC,CAAC;AACNyB,YAAY,CAACW,WAAW,GAAG,CAAC,cAAc,CAAC;AAC3C,MAAMC,OAAO,GAAGnC,OAAO,CAACW,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEC,UAAU,EAAEC,WAAW,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,eAAe,EAAEG,YAAY,EAAEF,OAAO,EAAEC,YAAY,CAAC;AACzM,eAAea,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}