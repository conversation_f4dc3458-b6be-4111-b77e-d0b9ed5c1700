{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport { createGrid as createGrid2 } from '@mui/system/Unstable_Grid';\nimport { styled } from '../styles';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nconst Grid2 = createGrid2({\n  createStyledComponent: styled('div', {\n    name: 'MuiGrid2',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  componentName: 'MuiGrid2',\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: 'MuiGrid2'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Grid2.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Grid2;", "map": {"version": 3, "names": ["PropTypes", "createGrid", "createGrid2", "styled", "useDefaultProps", "Grid2", "createStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "root", "componentName", "useThemeProps", "inProps", "process", "env", "NODE_ENV", "propTypes", "children", "node", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/Unstable_Grid2/Grid2.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createGrid as createGrid2 } from '@mui/system/Unstable_Grid';\nimport { styled } from '../styles';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nconst Grid2 = createGrid2({\n  createStyledComponent: styled('div', {\n    name: 'MuiGrid2',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  componentName: 'MuiGrid2',\n  useThemeProps: inProps => useDefaultProps({\n    props: inProps,\n    name: 'MuiGrid2'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Grid2.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Grid2;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,IAAIC,WAAW,QAAQ,2BAA2B;AACrE,SAASC,MAAM,QAAQ,WAAW;AAClC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,MAAMC,KAAK,GAAGH,WAAW,CAAC;EACxBI,qBAAqB,EAAEH,MAAM,CAAC,KAAK,EAAE;IACnCI,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,MAAM;IACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;EAC/C,CAAC,CAAC;EACFC,aAAa,EAAE,UAAU;EACzBC,aAAa,EAAEC,OAAO,IAAIX,eAAe,CAAC;IACxCM,KAAK,EAAEK,OAAO;IACdR,IAAI,EAAE;EACR,CAAC;AACH,CAAC,CAAC;AACFS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGb,KAAK,CAACc,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEpB,SAAS,CAACqB,IAAI;EACxB;AACF;AACA;EACEC,EAAE,EAAEtB,SAAS,CAACuB,SAAS,CAAC,CAACvB,SAAS,CAACwB,OAAO,CAACxB,SAAS,CAACuB,SAAS,CAAC,CAACvB,SAAS,CAACyB,IAAI,EAAEzB,SAAS,CAAC0B,MAAM,EAAE1B,SAAS,CAAC2B,IAAI,CAAC,CAAC,CAAC,EAAE3B,SAAS,CAACyB,IAAI,EAAEzB,SAAS,CAAC0B,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAerB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}