{"ast": null, "code": "'use client';\n\nexport { default } from './StyledEngineProvider';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/styled-engine/StyledEngineProvider/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './StyledEngineProvider';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}