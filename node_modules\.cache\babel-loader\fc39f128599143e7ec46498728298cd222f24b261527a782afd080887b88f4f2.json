{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport platform from './node/index.js';\nimport * as utils from './common/utils.js';\nexport default _objectSpread(_objectSpread({}, utils), platform);", "map": {"version": 3, "names": ["platform", "utils", "_objectSpread"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/axios/lib/platform/index.js"], "sourcesContent": ["import platform from './node/index.js';\nimport * as utils from './common/utils.js';\n\nexport default {\n  ...utils,\n  ...platform\n}\n"], "mappings": ";AAAA,OAAOA,QAAQ,MAAM,iBAAiB;AACtC,OAAO,KAAKC,KAAK,MAAM,mBAAmB;AAE1C,eAAAC,aAAA,CAAAA,aAAA,KACKD,KAAK,GACLD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}