{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * NoSsr purposely removes components from the subject of Server Side Rendering (SSR).\n *\n * This component can be useful in a variety of situations:\n *\n * *   Escape hatch for broken dependencies not supporting SSR.\n * *   Improve the time-to-first paint on the client by only rendering above the fold.\n * *   Reduce the rendering time on the server.\n * *   Under too heavy server load, you can turn on service degradation.\n *\n * Demos:\n *\n * - [No SSR](https://mui.com/material-ui/react-no-ssr/)\n *\n * API:\n *\n * - [NoSsr API](https://mui.com/material-ui/api/no-ssr/)\n */\nfunction NoSsr(props) {\n  const {\n    children,\n    defer = false,\n    fallback = null\n  } = props;\n  const [mountedState, setMountedState] = React.useState(false);\n  useEnhancedEffect(() => {\n    if (!defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n  React.useEffect(() => {\n    if (defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n\n  // We need the Fragment here to force react-docgen to recognise NoSsr as a component.\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: mountedState ? children : fallback\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? NoSsr.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the component will not only prevent server-side rendering.\n   * It will also defer the rendering of the children into a different screen frame.\n   * @default false\n   */\n  defer: PropTypes.bool,\n  /**\n   * The fallback content to display.\n   * @default null\n   */\n  fallback: PropTypes.node\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  NoSsr['propTypes' + ''] = exactProp(NoSsr.propTypes);\n}\nexport default NoSsr;", "map": {"version": 3, "names": ["React", "PropTypes", "exactProp", "unstable_useEnhancedEffect", "useEnhancedEffect", "jsx", "_jsx", "NoSsr", "props", "children", "defer", "fallback", "mountedState", "setMountedState", "useState", "useEffect", "Fragment", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/NoSsr/NoSsr.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * NoSsr purposely removes components from the subject of Server Side Rendering (SSR).\n *\n * This component can be useful in a variety of situations:\n *\n * *   Escape hatch for broken dependencies not supporting SSR.\n * *   Improve the time-to-first paint on the client by only rendering above the fold.\n * *   Reduce the rendering time on the server.\n * *   Under too heavy server load, you can turn on service degradation.\n *\n * Demos:\n *\n * - [No SSR](https://mui.com/material-ui/react-no-ssr/)\n *\n * API:\n *\n * - [NoSsr API](https://mui.com/material-ui/api/no-ssr/)\n */\nfunction NoSsr(props) {\n  const {\n    children,\n    defer = false,\n    fallback = null\n  } = props;\n  const [mountedState, setMountedState] = React.useState(false);\n  useEnhancedEffect(() => {\n    if (!defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n  React.useEffect(() => {\n    if (defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n\n  // We need the Fragment here to force react-docgen to recognise NoSsr as a component.\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: mountedState ? children : fallback\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? NoSsr.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the component will not only prevent server-side rendering.\n   * It will also defer the rendering of the children into a different screen frame.\n   * @default false\n   */\n  defer: PropTypes.bool,\n  /**\n   * The fallback content to display.\n   * @default null\n   */\n  fallback: PropTypes.node\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  NoSsr['propTypes' + ''] = exactProp(NoSsr.propTypes);\n}\nexport default NoSsr;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AACvF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,MAAM;IACJC,QAAQ;IACRC,KAAK,GAAG,KAAK;IACbC,QAAQ,GAAG;EACb,CAAC,GAAGH,KAAK;EACT,MAAM,CAACI,YAAY,EAAEC,eAAe,CAAC,GAAGb,KAAK,CAACc,QAAQ,CAAC,KAAK,CAAC;EAC7DV,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAACM,KAAK,EAAE;MACVG,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;EACXV,KAAK,CAACe,SAAS,CAAC,MAAM;IACpB,IAAIL,KAAK,EAAE;MACTG,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACH,KAAK,CAAC,CAAC;;EAEX;EACA,OAAO,aAAaJ,IAAI,CAACN,KAAK,CAACgB,QAAQ,EAAE;IACvCP,QAAQ,EAAEG,YAAY,GAAGH,QAAQ,GAAGE;EACtC,CAAC,CAAC;AACJ;AACAM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,KAAK,CAACa,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEX,QAAQ,EAAER,SAAS,CAACoB,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEX,KAAK,EAAET,SAAS,CAACqB,IAAI;EACrB;AACF;AACA;AACA;EACEX,QAAQ,EAAEV,SAAS,CAACoB;AACtB,CAAC,GAAG,KAAK,CAAC;AACV,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACAZ,KAAK,CAAC,WAAW,GAAG,EAAE,CAAC,GAAGL,SAAS,CAACK,KAAK,CAACa,SAAS,CAAC;AACtD;AACA,eAAeb,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}