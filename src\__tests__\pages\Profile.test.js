import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders, mockAuthResponses, mockUserData } from '../utils/testUtils';
import Profile from '../../pages/Profile';
import authService from '../../api/authService';

// Mock the authService
jest.mock('../../api/authService');
const mockedAuthService = authService;

// Mock the navigate function
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('Profile Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockNavigate.mockClear();
  });

  describe('Loading State', () => {
    it('should show loading spinner initially', () => {
      mockedAuthService.getProfile.mockImplementation(() => new Promise(() => {})); // Never resolves

      renderWithProviders(<Profile />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
      expect(screen.queryByText('Profile')).not.toBeInTheDocument();
    });

    it('should call getProfile on component mount', () => {
      mockedAuthService.getProfile.mockResolvedValueOnce(mockAuthResponses.profileSuccess.data);

      renderWithProviders(<Profile />);

      expect(mockedAuthService.getProfile).toHaveBeenCalledTimes(1);
      expect(mockedAuthService.getProfile).toHaveBeenCalledWith(
        expect.any(Function),
        expect.any(Function)
      );
    });
  });

  describe('Successful Profile Load', () => {
    beforeEach(() => {
      mockedAuthService.getProfile.mockResolvedValueOnce(mockAuthResponses.profileSuccess.data);
    });

    it('should display user profile data after successful load', async () => {
      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument();
        expect(screen.getByText(`Name: ${mockUserData.name}`)).toBeInTheDocument();
        expect(screen.getByText(`Email: ${mockUserData.email}`)).toBeInTheDocument();
      });

      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });

    it('should display user avatar with first letter of name', async () => {
      renderWithProviders(<Profile />);

      await waitFor(() => {
        const avatar = screen.getByText(mockUserData.name.charAt(0).toUpperCase());
        expect(avatar).toBeInTheDocument();
      });
    });

    it('should display logout button', async () => {
      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /logout/i })).toBeInTheDocument();
      });
    });

    it('should handle logout button click', async () => {
      const user = userEvent.setup();
      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /logout/i })).toBeInTheDocument();
      });

      const logoutButton = screen.getByRole('button', { name: /logout/i });
      await user.click(logoutButton);

      expect(mockedAuthService.logout).toHaveBeenCalledTimes(1);
      expect(mockNavigate).toHaveBeenCalledWith('/login');
    });
  });

  describe('Error Handling', () => {
    it('should display error message when profile fetch fails', async () => {
      const errorMessage = 'Failed to fetch profile';
      mockedAuthService.getProfile.mockRejectedValueOnce(errorMessage);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
      expect(screen.queryByText('Profile')).not.toBeInTheDocument();
    });

    it('should handle unauthorized error', async () => {
      const errorMessage = 'Unauthorized';
      mockedAuthService.getProfile.mockRejectedValueOnce(errorMessage);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });
    });

    it('should close error snackbar when close button is clicked', async () => {
      const user = userEvent.setup();
      const errorMessage = 'Failed to fetch profile';
      mockedAuthService.getProfile.mockRejectedValueOnce(errorMessage);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      const closeButton = screen.getByLabelText('Close');
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByText(errorMessage)).not.toBeInTheDocument();
      });
    });

    it('should auto-hide error snackbar after timeout', async () => {
      const errorMessage = 'Failed to fetch profile';
      mockedAuthService.getProfile.mockRejectedValueOnce(errorMessage);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument();
      });

      // Wait for auto-hide (6 seconds + buffer)
      await waitFor(() => {
        expect(screen.queryByText(errorMessage)).not.toBeInTheDocument();
      }, { timeout: 7000 });
    });
  });

  describe('Edge Cases', () => {
    it('should handle profile data without name', async () => {
      const userDataWithoutName = { ...mockUserData, name: '' };
      mockedAuthService.getProfile.mockResolvedValueOnce(userDataWithoutName);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText('U')).toBeInTheDocument(); // Default avatar letter
        expect(screen.getByText('Name:')).toBeInTheDocument();
      });
    });

    it('should handle profile data with null name', async () => {
      const userDataWithNullName = { ...mockUserData, name: null };
      mockedAuthService.getProfile.mockResolvedValueOnce(userDataWithNullName);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText('U')).toBeInTheDocument(); // Default avatar letter
      });
    });

    it('should handle profile data without email', async () => {
      const userDataWithoutEmail = { ...mockUserData, email: '' };
      mockedAuthService.getProfile.mockResolvedValueOnce(userDataWithoutEmail);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText('Email:')).toBeInTheDocument();
      });
    });

    it('should handle empty profile response', async () => {
      mockedAuthService.getProfile.mockResolvedValueOnce(null);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument();
        expect(screen.queryByText('Name:')).not.toBeInTheDocument();
        expect(screen.queryByText('Email:')).not.toBeInTheDocument();
      });
    });

    it('should handle network error', async () => {
      const networkError = new Error('Network Error');
      mockedAuthService.getProfile.mockRejectedValueOnce(networkError);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByText('Network Error')).toBeInTheDocument();
      });
    });

    it('should handle multiple logout clicks', async () => {
      const user = userEvent.setup();
      mockedAuthService.getProfile.mockResolvedValueOnce(mockAuthResponses.profileSuccess.data);

      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /logout/i })).toBeInTheDocument();
      });

      const logoutButton = screen.getByRole('button', { name: /logout/i });
      
      // Click logout multiple times rapidly
      await user.click(logoutButton);
      await user.click(logoutButton);
      await user.click(logoutButton);

      expect(mockedAuthService.logout).toHaveBeenCalledTimes(3);
      expect(mockNavigate).toHaveBeenCalledTimes(3);
      expect(mockNavigate).toHaveBeenCalledWith('/login');
    });
  });

  describe('Accessibility', () => {
    beforeEach(() => {
      mockedAuthService.getProfile.mockResolvedValueOnce(mockAuthResponses.profileSuccess.data);
    });

    it('should have proper heading structure', async () => {
      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByRole('heading', { name: /profile/i })).toBeInTheDocument();
      });
    });

    it('should have proper button roles', async () => {
      renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /logout/i })).toBeInTheDocument();
      });
    });

    it('should have accessible avatar', async () => {
      renderWithProviders(<Profile />);

      await waitFor(() => {
        const avatar = screen.getByText(mockUserData.name.charAt(0).toUpperCase());
        expect(avatar).toBeInTheDocument();
      });
    });
  });

  describe('Component Lifecycle', () => {
    it('should only fetch profile once on mount', async () => {
      mockedAuthService.getProfile.mockResolvedValueOnce(mockAuthResponses.profileSuccess.data);

      const { rerender } = renderWithProviders(<Profile />);

      await waitFor(() => {
        expect(mockedAuthService.getProfile).toHaveBeenCalledTimes(1);
      });

      // Re-render component
      rerender(<Profile />);

      // Should not fetch again
      expect(mockedAuthService.getProfile).toHaveBeenCalledTimes(1);
    });

    it('should handle component unmount gracefully', async () => {
      mockedAuthService.getProfile.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      const { unmount } = renderWithProviders(<Profile />);

      // Unmount before promise resolves
      unmount();

      // Should not throw any errors
      expect(true).toBe(true);
    });
  });
});
