{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport ListContext from '../List/ListContext';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getListItemAvatarUtilityClass } from './listItemAvatarClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', alignItems === 'flex-start' && 'alignItemsFlexStart']\n  };\n  return composeClasses(slots, getListItemAvatarUtilityClass, classes);\n};\nconst ListItemAvatarRoot = styled('div', {\n  name: 'MuiListItemAvatar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    minWidth: 56,\n    flexShrink: 0\n  }, ownerState.alignItems === 'flex-start' && {\n    marginTop: 8\n  });\n});\n\n/**\n * A simple wrapper to apply `List` styles to an `Avatar`.\n */\nconst ListItemAvatar = /*#__PURE__*/React.forwardRef(function ListItemAvatar(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemAvatar'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = React.useContext(ListContext);\n  const ownerState = _extends({}, props, {\n    alignItems: context.alignItems\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemAvatarRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemAvatar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `Avatar`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemAvatar;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "ListContext", "styled", "useDefaultProps", "getListItemAvatarUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "alignItems", "classes", "slots", "root", "ListItemAvatarRoot", "name", "slot", "overridesResolver", "props", "styles", "alignItemsFlexStart", "_ref", "min<PERSON><PERSON><PERSON>", "flexShrink", "marginTop", "ListItemAvatar", "forwardRef", "inProps", "ref", "className", "other", "context", "useContext", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/ListItemAvatar/ListItemAvatar.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport ListContext from '../List/ListContext';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getListItemAvatarUtilityClass } from './listItemAvatarClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', alignItems === 'flex-start' && 'alignItemsFlexStart']\n  };\n  return composeClasses(slots, getListItemAvatarUtilityClass, classes);\n};\nconst ListItemAvatarRoot = styled('div', {\n  name: 'MuiListItemAvatar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart];\n  }\n})(({\n  ownerState\n}) => _extends({\n  minWidth: 56,\n  flexShrink: 0\n}, ownerState.alignItems === 'flex-start' && {\n  marginTop: 8\n}));\n\n/**\n * A simple wrapper to apply `List` styles to an `Avatar`.\n */\nconst ListItemAvatar = /*#__PURE__*/React.forwardRef(function ListItemAvatar(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemAvatar'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = React.useContext(ListContext);\n  const ownerState = _extends({}, props, {\n    alignItems: context.alignItems\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemAvatarRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemAvatar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `Avatar`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemAvatar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,UAAU;IACVC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,UAAU,KAAK,YAAY,IAAI,qBAAqB;EACrE,CAAC;EACD,OAAOT,cAAc,CAACW,KAAK,EAAEP,6BAA6B,EAAEM,OAAO,CAAC;AACtE,CAAC;AACD,MAAMG,kBAAkB,GAAGX,MAAM,CAAC,KAAK,EAAE;EACvCY,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEJ,UAAU,CAACC,UAAU,KAAK,YAAY,IAAIS,MAAM,CAACC,mBAAmB,CAAC;EAC5F;AACF,CAAC,CAAC,CAACC,IAAA;EAAA,IAAC;IACFZ;EACF,CAAC,GAAAY,IAAA;EAAA,OAAKzB,QAAQ,CAAC;IACb0B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC,EAAEd,UAAU,CAACC,UAAU,KAAK,YAAY,IAAI;IAC3Cc,SAAS,EAAE;EACb,CAAC,CAAC;AAAA,EAAC;;AAEH;AACA;AACA;AACA,MAAMC,cAAc,GAAG,aAAa3B,KAAK,CAAC4B,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAMV,KAAK,GAAGd,eAAe,CAAC;IAC5Bc,KAAK,EAAES,OAAO;IACdZ,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFc;IACF,CAAC,GAAGX,KAAK;IACTY,KAAK,GAAGnC,6BAA6B,CAACuB,KAAK,EAAErB,SAAS,CAAC;EACzD,MAAMkC,OAAO,GAAGjC,KAAK,CAACkC,UAAU,CAAC9B,WAAW,CAAC;EAC7C,MAAMO,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCR,UAAU,EAAEqB,OAAO,CAACrB;EACtB,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGH,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,kBAAkB,EAAElB,QAAQ,CAAC;IACpDiC,SAAS,EAAE7B,IAAI,CAACW,OAAO,CAACE,IAAI,EAAEgB,SAAS,CAAC;IACxCpB,UAAU,EAAEA,UAAU;IACtBmB,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,cAAc,CAACW,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEtC,SAAS,CAACuC,IAAI;EACxB;AACF;AACA;EACE3B,OAAO,EAAEZ,SAAS,CAACwC,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAE9B,SAAS,CAACyC,MAAM;EAC3B;AACF;AACA;EACEC,EAAE,EAAE1C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC4C,OAAO,CAAC5C,SAAS,CAAC2C,SAAS,CAAC,CAAC3C,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACwC,MAAM,EAAExC,SAAS,CAAC8C,IAAI,CAAC,CAAC,CAAC,EAAE9C,SAAS,CAAC6C,IAAI,EAAE7C,SAAS,CAACwC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAed,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}