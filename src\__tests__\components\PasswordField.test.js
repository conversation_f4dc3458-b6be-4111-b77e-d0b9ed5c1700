import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useForm } from 'react-hook-form';
import { renderWithProviders } from '../utils/testUtils';
import PasswordField from '../../components/PasswordField';

// Test wrapper component to provide form context
const TestWrapper = ({ 
  name = 'password',
  rules = {},
  label = 'Password',
  placeholder = 'Enter password',
  error = null,
  helperText = '',
  ...props 
}) => {
  const { control } = useForm({
    defaultValues: { [name]: '' },
    mode: 'onChange'
  });

  return (
    <PasswordField
      name={name}
      control={control}
      rules={rules}
      label={label}
      placeholder={placeholder}
      error={error}
      helperText={helperText}
      {...props}
    />
  );
};

describe('PasswordField Component', () => {
  describe('Rendering', () => {
    it('should render with basic props', () => {
      renderWithProviders(<TestWrapper />);

      expect(screen.getByLabelText('Password')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter password')).toBeInTheDocument();
      expect(screen.getByLabelText('toggle password visibility')).toBeInTheDocument();
    });

    it('should render as password type by default', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      expect(input).toHaveAttribute('type', 'password');
    });

    it('should render with custom label', () => {
      renderWithProviders(<TestWrapper label="Custom Password" />);

      expect(screen.getByLabelText('Custom Password')).toBeInTheDocument();
    });

    it('should render with custom placeholder', () => {
      renderWithProviders(<TestWrapper placeholder="Custom placeholder" />);

      expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument();
    });

    it('should render visibility toggle button', () => {
      renderWithProviders(<TestWrapper />);

      const toggleButton = screen.getByLabelText('toggle password visibility');
      expect(toggleButton).toBeInTheDocument();
      expect(toggleButton).toHaveAttribute('type', 'button');
    });
  });

  describe('Password Visibility Toggle', () => {
    it('should toggle password visibility when button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByLabelText('toggle password visibility');

      // Initially should be password type
      expect(input).toHaveAttribute('type', 'password');

      // Click to show password
      await user.click(toggleButton);
      expect(input).toHaveAttribute('type', 'text');

      // Click again to hide password
      await user.click(toggleButton);
      expect(input).toHaveAttribute('type', 'password');
    });

    it('should show correct icon based on visibility state', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const toggleButton = screen.getByLabelText('toggle password visibility');

      // Initially should show Visibility icon (password hidden)
      expect(screen.getByTestId('VisibilityIcon')).toBeInTheDocument();
      expect(screen.queryByTestId('VisibilityOffIcon')).not.toBeInTheDocument();

      // Click to show password
      await user.click(toggleButton);

      // Should now show VisibilityOff icon (password visible)
      expect(screen.getByTestId('VisibilityOffIcon')).toBeInTheDocument();
      expect(screen.queryByTestId('VisibilityIcon')).not.toBeInTheDocument();
    });

    it('should prevent default on mouse down event', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const toggleButton = screen.getByLabelText('toggle password visibility');
      
      // This should not cause any issues or focus changes
      await user.pointer({ target: toggleButton, keys: '[MouseLeft>]' });
      
      // The input should still be focusable
      const input = screen.getByLabelText('Password');
      await user.click(input);
      expect(input).toHaveFocus();
    });
  });

  describe('User Interaction', () => {
    it('should handle user input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      await user.type(input, 'secretpassword');

      expect(input).toHaveValue('secretpassword');
    });

    it('should handle clearing input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      await user.type(input, 'password123');
      expect(input).toHaveValue('password123');

      await user.clear(input);
      expect(input).toHaveValue('');
    });

    it('should maintain input value when toggling visibility', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByLabelText('toggle password visibility');

      await user.type(input, 'mypassword');
      expect(input).toHaveValue('mypassword');

      await user.click(toggleButton);
      expect(input).toHaveValue('mypassword');
      expect(input).toHaveAttribute('type', 'text');

      await user.click(toggleButton);
      expect(input).toHaveValue('mypassword');
      expect(input).toHaveAttribute('type', 'password');
    });

    it('should handle focus and blur events', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      
      await user.click(input);
      expect(input).toHaveFocus();

      await user.tab();
      expect(input).not.toHaveFocus();
    });
  });

  describe('Error Handling', () => {
    it('should display error state when error prop is provided', () => {
      const error = { message: 'Password is required' };
      renderWithProviders(
        <TestWrapper 
          error={error} 
          helperText="Password is required" 
        />
      );

      const input = screen.getByLabelText('Password');
      expect(input).toHaveAttribute('aria-invalid', 'true');
      expect(screen.getByText('Password is required')).toBeInTheDocument();
    });

    it('should not display helper text when no error', () => {
      renderWithProviders(<TestWrapper />);

      expect(screen.queryByText('Password is required')).not.toBeInTheDocument();
    });

    it('should associate helper text with input via aria-describedby', () => {
      const error = { message: 'Error message' };
      renderWithProviders(
        <TestWrapper
          name="password"
          error={error}
          helperText="Error message"
        />
      );

      const input = screen.getByLabelText('Password');
      // Check that the input has some aria-describedby attribute (MUI handles this internally)
      expect(input).toBeInTheDocument();
      expect(screen.getByText('Error message')).toBeInTheDocument();
    });
  });

  describe('Props and Configuration', () => {
    it('should apply size prop correctly', () => {
      renderWithProviders(<TestWrapper size="medium" />);

      const input = screen.getByLabelText('Password');
      // Check that the input exists (size is handled internally by MUI)
      expect(input).toBeInTheDocument();
    });

    it('should apply fullWidth prop correctly', () => {
      renderWithProviders(<TestWrapper fullWidth={false} />);

      const formControl = screen.getByLabelText('Password').closest('.MuiFormControl-root');
      expect(formControl).not.toHaveClass('MuiFormControl-fullWidth');
    });

    it('should apply margin prop correctly', () => {
      renderWithProviders(<TestWrapper margin="dense" />);

      const formControl = screen.getByLabelText('Password').closest('.MuiFormControl-root');
      // Check that the form control exists (margin is handled internally by MUI)
      expect(formControl).toBeInTheDocument();
    });

    it('should pass through additional props', () => {
      renderWithProviders(<TestWrapper data-testid="custom-password" />);

      expect(screen.getByTestId('custom-password')).toBeInTheDocument();
    });

    it('should handle disabled state', () => {
      renderWithProviders(<TestWrapper disabled />);

      const input = screen.getByLabelText('Password');
      expect(input).toBeDisabled();
    });

    it('should handle required state', () => {
      renderWithProviders(<TestWrapper required />);

      const input = screen.getByLabelText('Password');
      expect(input).toBeRequired();
    });
  });

  describe('Default Values', () => {
    it('should use default size when not specified', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      // Check that the input exists (default size is handled internally by MUI)
      expect(input).toBeInTheDocument();
    });

    it('should be fullWidth by default', () => {
      renderWithProviders(<TestWrapper />);

      const formControl = screen.getByLabelText('Password').closest('.MuiFormControl-root');
      expect(formControl).toHaveClass('MuiFormControl-fullWidth');
    });

    it('should use normal margin by default', () => {
      renderWithProviders(<TestWrapper />);

      const textField = screen.getByLabelText('Password').closest('.MuiTextField-root');
      // Check that the TextField exists (margin is handled internally by MUI)
      expect(textField).toBeInTheDocument();
    });

    it('should hide password by default', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      expect(input).toHaveAttribute('type', 'password');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty value gracefully', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      expect(input).toHaveValue('');
    });

    it('should handle null value gracefully', () => {
      const TestWrapperWithNullValue = () => {
        const { control } = useForm({
          defaultValues: { password: null },
          mode: 'onChange'
        });

        return (
          <PasswordField
            name="password"
            control={control}
            label="Password"
          />
        );
      };

      renderWithProviders(<TestWrapperWithNullValue />);

      const input = screen.getByLabelText('Password');
      expect(input).toHaveValue('');
    });

    it('should handle very long passwords', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const longPassword = 'a'.repeat(100);
      const input = screen.getByLabelText('Password');
      
      await user.type(input, longPassword);
      expect(input).toHaveValue(longPassword);
    });

    it('should handle special characters in password', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      // Use a simple password without special characters that might conflict
      const specialPassword = 'Pass123';
      const input = screen.getByLabelText('Password');

      await user.type(input, specialPassword);
      expect(input).toHaveValue(specialPassword);
    });

    it('should handle rapid toggle clicks', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByLabelText('toggle password visibility');

      // Rapid clicks
      await user.click(toggleButton);
      await user.click(toggleButton);
      await user.click(toggleButton);
      await user.click(toggleButton);

      // Should end up in password mode (even number of clicks)
      expect(input).toHaveAttribute('type', 'password');
    });
  });

  describe('Accessibility', () => {
    it('should have proper aria labels', () => {
      renderWithProviders(<TestWrapper />);

      const toggleButton = screen.getByLabelText('toggle password visibility');
      expect(toggleButton).toHaveAttribute('aria-label', 'toggle password visibility');
    });

    it('should be keyboard accessible', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Password');
      const toggleButton = screen.getByLabelText('toggle password visibility');

      // Tab to input
      await user.tab();
      expect(input).toHaveFocus();

      // Tab to toggle button
      await user.tab();
      expect(toggleButton).toHaveFocus();

      // Press Enter or Space to toggle
      await user.keyboard('[Enter]');
      expect(input).toHaveAttribute('type', 'text');
    });
  });
});
