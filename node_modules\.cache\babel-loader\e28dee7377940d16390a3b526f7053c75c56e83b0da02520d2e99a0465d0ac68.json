{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"component\", \"disabled\", \"error\", \"filled\", \"focused\", \"required\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport capitalize from '../utils/capitalize';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport formLabelClasses, { getFormLabelUtilityClasses } from './formLabelClasses';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    color,\n    focused,\n    disabled,\n    error,\n    filled,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', \"color\".concat(capitalize(color)), disabled && 'disabled', error && 'error', filled && 'filled', focused && 'focused', required && 'required'],\n    asterisk: ['asterisk', error && 'error']\n  };\n  return composeClasses(slots, getFormLabelUtilityClasses, classes);\n};\nexport const FormLabelRoot = styled('label', {\n  name: 'MuiFormLabel',\n  slot: 'Root',\n  overridesResolver: (_ref, styles) => {\n    let {\n      ownerState\n    } = _ref;\n    return _extends({}, styles.root, ownerState.color === 'secondary' && styles.colorSecondary, ownerState.filled && styles.filled);\n  }\n})(_ref2 => {\n  let {\n    theme,\n    ownerState\n  } = _ref2;\n  return _extends({\n    color: (theme.vars || theme).palette.text.secondary\n  }, theme.typography.body1, {\n    lineHeight: '1.4375em',\n    padding: 0,\n    position: 'relative',\n    [\"&.\".concat(formLabelClasses.focused)]: {\n      color: (theme.vars || theme).palette[ownerState.color].main\n    },\n    [\"&.\".concat(formLabelClasses.disabled)]: {\n      color: (theme.vars || theme).palette.text.disabled\n    },\n    [\"&.\".concat(formLabelClasses.error)]: {\n      color: (theme.vars || theme).palette.error.main\n    }\n  });\n});\nconst AsteriskComponent = styled('span', {\n  name: 'MuiFormLabel',\n  slot: 'Asterisk',\n  overridesResolver: (props, styles) => styles.asterisk\n})(_ref3 => {\n  let {\n    theme\n  } = _ref3;\n  return {\n    [\"&.\".concat(formLabelClasses.error)]: {\n      color: (theme.vars || theme).palette.error.main\n    }\n  };\n});\nconst FormLabel = /*#__PURE__*/React.forwardRef(function FormLabel(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormLabel'\n  });\n  const {\n      children,\n      className,\n      component = 'label'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['color', 'required', 'focused', 'disabled', 'error', 'filled']\n  });\n  const ownerState = _extends({}, props, {\n    color: fcs.color || 'primary',\n    component,\n    disabled: fcs.disabled,\n    error: fcs.error,\n    filled: fcs.filled,\n    focused: fcs.focused,\n    required: fcs.required\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(FormLabelRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [children, fcs.required && /*#__PURE__*/_jsxs(AsteriskComponent, {\n      ownerState: ownerState,\n      \"aria-hidden\": true,\n      className: classes.asterisk,\n      children: [\"\\u2009\", '*']\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['error', 'info', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the label should be displayed in a disabled state.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the label should use filled classes key.\n   */\n  filled: PropTypes.bool,\n  /**\n   * If `true`, the input of this label is focused (used by `FormGroup` components).\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormLabel;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "formControlState", "useFormControl", "capitalize", "useDefaultProps", "styled", "formLabelClasses", "getFormLabelUtilityClasses", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "color", "focused", "disabled", "error", "filled", "required", "slots", "root", "concat", "asterisk", "FormLabelRoot", "name", "slot", "overridesResolver", "_ref", "styles", "colorSecondary", "_ref2", "theme", "vars", "palette", "text", "secondary", "typography", "body1", "lineHeight", "padding", "position", "main", "AsteriskComponent", "props", "_ref3", "FormLabel", "forwardRef", "inProps", "ref", "children", "className", "component", "other", "muiFormControl", "fcs", "states", "as", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "oneOfType", "oneOf", "elementType", "bool", "sx", "arrayOf", "func"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/FormLabel/FormLabel.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"component\", \"disabled\", \"error\", \"filled\", \"focused\", \"required\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport capitalize from '../utils/capitalize';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport formLabelClasses, { getFormLabelUtilityClasses } from './formLabelClasses';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    color,\n    focused,\n    disabled,\n    error,\n    filled,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', `color${capitalize(color)}`, disabled && 'disabled', error && 'error', filled && 'filled', focused && 'focused', required && 'required'],\n    asterisk: ['asterisk', error && 'error']\n  };\n  return composeClasses(slots, getFormLabelUtilityClasses, classes);\n};\nexport const FormLabelRoot = styled('label', {\n  name: 'MuiFormLabel',\n  slot: 'Root',\n  overridesResolver: ({\n    ownerState\n  }, styles) => {\n    return _extends({}, styles.root, ownerState.color === 'secondary' && styles.colorSecondary, ownerState.filled && styles.filled);\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  color: (theme.vars || theme).palette.text.secondary\n}, theme.typography.body1, {\n  lineHeight: '1.4375em',\n  padding: 0,\n  position: 'relative',\n  [`&.${formLabelClasses.focused}`]: {\n    color: (theme.vars || theme).palette[ownerState.color].main\n  },\n  [`&.${formLabelClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.text.disabled\n  },\n  [`&.${formLabelClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}));\nconst AsteriskComponent = styled('span', {\n  name: 'MuiFormLabel',\n  slot: 'Asterisk',\n  overridesResolver: (props, styles) => styles.asterisk\n})(({\n  theme\n}) => ({\n  [`&.${formLabelClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}));\nconst FormLabel = /*#__PURE__*/React.forwardRef(function FormLabel(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormLabel'\n  });\n  const {\n      children,\n      className,\n      component = 'label'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['color', 'required', 'focused', 'disabled', 'error', 'filled']\n  });\n  const ownerState = _extends({}, props, {\n    color: fcs.color || 'primary',\n    component,\n    disabled: fcs.disabled,\n    error: fcs.error,\n    filled: fcs.filled,\n    focused: fcs.focused,\n    required: fcs.required\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(FormLabelRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: [children, fcs.required && /*#__PURE__*/_jsxs(AsteriskComponent, {\n      ownerState: ownerState,\n      \"aria-hidden\": true,\n      className: classes.asterisk,\n      children: [\"\\u2009\", '*']\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['error', 'info', 'primary', 'secondary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the label should be displayed in a disabled state.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the label should use filled classes key.\n   */\n  filled: PropTypes.bool,\n  /**\n   * If `true`, the input of this label is focused (used by `FormGroup` components).\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormLabel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;AACvH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,gBAAgB,IAAIC,0BAA0B,QAAQ,oBAAoB;AACjF,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,KAAK;IACLC,OAAO;IACPC,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC;EACF,CAAC,GAAGP,UAAU;EACd,MAAMQ,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,UAAAC,MAAA,CAAUlB,UAAU,CAACU,KAAK,CAAC,GAAIE,QAAQ,IAAI,UAAU,EAAEC,KAAK,IAAI,OAAO,EAAEC,MAAM,IAAI,QAAQ,EAAEH,OAAO,IAAI,SAAS,EAAEI,QAAQ,IAAI,UAAU,CAAC;IACvJI,QAAQ,EAAE,CAAC,UAAU,EAAEN,KAAK,IAAI,OAAO;EACzC,CAAC;EACD,OAAOhB,cAAc,CAACmB,KAAK,EAAEZ,0BAA0B,EAAEK,OAAO,CAAC;AACnE,CAAC;AACD,OAAO,MAAMW,aAAa,GAAGlB,MAAM,CAAC,OAAO,EAAE;EAC3CmB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAAAC,IAAA,EAEhBC,MAAM,KAAK;IAAA,IAFM;MAClBjB;IACF,CAAC,GAAAgB,IAAA;IACC,OAAOhC,QAAQ,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAACR,IAAI,EAAET,UAAU,CAACE,KAAK,KAAK,WAAW,IAAIe,MAAM,CAACC,cAAc,EAAElB,UAAU,CAACM,MAAM,IAAIW,MAAM,CAACX,MAAM,CAAC;EACjI;AACF,CAAC,CAAC,CAACa,KAAA;EAAA,IAAC;IACFC,KAAK;IACLpB;EACF,CAAC,GAAAmB,KAAA;EAAA,OAAKnC,QAAQ,CAAC;IACbkB,KAAK,EAAE,CAACkB,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACC,IAAI,CAACC;EAC5C,CAAC,EAAEJ,KAAK,CAACK,UAAU,CAACC,KAAK,EAAE;IACzBC,UAAU,EAAE,UAAU;IACtBC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,UAAU;IACpB,MAAAnB,MAAA,CAAMf,gBAAgB,CAACQ,OAAO,IAAK;MACjCD,KAAK,EAAE,CAACkB,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACtB,UAAU,CAACE,KAAK,CAAC,CAAC4B;IACzD,CAAC;IACD,MAAApB,MAAA,CAAMf,gBAAgB,CAACS,QAAQ,IAAK;MAClCF,KAAK,EAAE,CAACkB,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACC,IAAI,CAACnB;IAC5C,CAAC;IACD,MAAAM,MAAA,CAAMf,gBAAgB,CAACU,KAAK,IAAK;MAC/BH,KAAK,EAAE,CAACkB,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACjB,KAAK,CAACyB;IAC7C;EACF,CAAC,CAAC;AAAA,EAAC;AACH,MAAMC,iBAAiB,GAAGrC,MAAM,CAAC,MAAM,EAAE;EACvCmB,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAEA,CAACiB,KAAK,EAAEf,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACsB,KAAA;EAAA,IAAC;IACFb;EACF,CAAC,GAAAa,KAAA;EAAA,OAAM;IACL,MAAAvB,MAAA,CAAMf,gBAAgB,CAACU,KAAK,IAAK;MAC/BH,KAAK,EAAE,CAACkB,KAAK,CAACC,IAAI,IAAID,KAAK,EAAEE,OAAO,CAACjB,KAAK,CAACyB;IAC7C;EACF,CAAC;AAAA,CAAC,CAAC;AACH,MAAMI,SAAS,GAAG,aAAahD,KAAK,CAACiD,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,MAAML,KAAK,GAAGvC,eAAe,CAAC;IAC5BuC,KAAK,EAAEI,OAAO;IACdvB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFyB,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG;IACd,CAAC,GAAGR,KAAK;IACTS,KAAK,GAAG1D,6BAA6B,CAACiD,KAAK,EAAE/C,SAAS,CAAC;EACzD,MAAMyD,cAAc,GAAGnD,cAAc,CAAC,CAAC;EACvC,MAAMoD,GAAG,GAAGrD,gBAAgB,CAAC;IAC3B0C,KAAK;IACLU,cAAc;IACdE,MAAM,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ;EACxE,CAAC,CAAC;EACF,MAAM5C,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,EAAE;IACrC9B,KAAK,EAAEyC,GAAG,CAACzC,KAAK,IAAI,SAAS;IAC7BsC,SAAS;IACTpC,QAAQ,EAAEuC,GAAG,CAACvC,QAAQ;IACtBC,KAAK,EAAEsC,GAAG,CAACtC,KAAK;IAChBC,MAAM,EAAEqC,GAAG,CAACrC,MAAM;IAClBH,OAAO,EAAEwC,GAAG,CAACxC,OAAO;IACpBI,QAAQ,EAAEoC,GAAG,CAACpC;EAChB,CAAC,CAAC;EACF,MAAMN,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,KAAK,CAACc,aAAa,EAAE5B,QAAQ,CAAC;IAChD6D,EAAE,EAAEL,SAAS;IACbxC,UAAU,EAAEA,UAAU;IACtBuC,SAAS,EAAEnD,IAAI,CAACa,OAAO,CAACQ,IAAI,EAAE8B,SAAS,CAAC;IACxCF,GAAG,EAAEA;EACP,CAAC,EAAEI,KAAK,EAAE;IACRH,QAAQ,EAAE,CAACA,QAAQ,EAAEK,GAAG,CAACpC,QAAQ,IAAI,aAAaT,KAAK,CAACiC,iBAAiB,EAAE;MACzE/B,UAAU,EAAEA,UAAU;MACtB,aAAa,EAAE,IAAI;MACnBuC,SAAS,EAAEtC,OAAO,CAACU,QAAQ;MAC3B2B,QAAQ,EAAE,CAAC,QAAQ,EAAE,GAAG;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGd,SAAS,CAACe,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEX,QAAQ,EAAEnD,SAAS,CAAC+D,IAAI;EACxB;AACF;AACA;EACEjD,OAAO,EAAEd,SAAS,CAACgE,MAAM;EACzB;AACF;AACA;EACEZ,SAAS,EAAEpD,SAAS,CAACiE,MAAM;EAC3B;AACF;AACA;AACA;AACA;EACElD,KAAK,EAAEf,SAAS,CAAC,sCAAsCkE,SAAS,CAAC,CAAClE,SAAS,CAACmE,KAAK,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAEnE,SAAS,CAACiE,MAAM,CAAC,CAAC;EACtK;AACF;AACA;AACA;EACEZ,SAAS,EAAErD,SAAS,CAACoE,WAAW;EAChC;AACF;AACA;EACEnD,QAAQ,EAAEjB,SAAS,CAACqE,IAAI;EACxB;AACF;AACA;EACEnD,KAAK,EAAElB,SAAS,CAACqE,IAAI;EACrB;AACF;AACA;EACElD,MAAM,EAAEnB,SAAS,CAACqE,IAAI;EACtB;AACF;AACA;EACErD,OAAO,EAAEhB,SAAS,CAACqE,IAAI;EACvB;AACF;AACA;EACEjD,QAAQ,EAAEpB,SAAS,CAACqE,IAAI;EACxB;AACF;AACA;EACEC,EAAE,EAAEtE,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACuE,OAAO,CAACvE,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACgE,MAAM,EAAEhE,SAAS,CAACqE,IAAI,CAAC,CAAC,CAAC,EAAErE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACgE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAejB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}