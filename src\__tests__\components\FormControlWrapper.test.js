import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useForm } from 'react-hook-form';
import { renderWithProviders } from '../utils/testUtils';
import FormControlWrapper from '../../components/FormControlWrapper';

// Test wrapper component to provide form context
const TestWrapper = ({ 
  name = 'testField',
  rules = {},
  label = 'Test Label',
  type = 'text',
  placeholder = 'Test placeholder',
  error = null,
  helperText = '',
  ...props 
}) => {
  const { control } = useForm({
    defaultValues: { [name]: '' },
    mode: 'onChange'
  });

  return (
    <FormControlWrapper
      name={name}
      control={control}
      rules={rules}
      label={label}
      type={type}
      placeholder={placeholder}
      error={error}
      helperText={helperText}
      {...props}
    />
  );
};

describe('FormControlWrapper Component', () => {
  describe('Rendering', () => {
    it('should render with basic props', () => {
      renderWithProviders(<TestWrapper />);

      expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Test placeholder')).toBeInTheDocument();
    });

    it('should render with different input types', () => {
      renderWithProviders(<TestWrapper type="email" />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveAttribute('type', 'email');
    });

    it('should render with custom label', () => {
      renderWithProviders(<TestWrapper label="Custom Label" />);

      expect(screen.getByLabelText('Custom Label')).toBeInTheDocument();
    });

    it('should render with custom placeholder', () => {
      renderWithProviders(<TestWrapper placeholder="Custom placeholder" />);

      expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument();
    });

    it('should render without error state by default', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      expect(input).not.toHaveAttribute('aria-invalid', 'true');
    });
  });

  describe('Error Handling', () => {
    it('should display error state when error prop is provided', () => {
      const error = { message: 'This field is required' };
      renderWithProviders(
        <TestWrapper 
          error={error} 
          helperText="This field is required" 
        />
      );

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveAttribute('aria-invalid', 'true');
      expect(screen.getByText('This field is required')).toBeInTheDocument();
    });

    it('should not display helper text when no error', () => {
      renderWithProviders(<TestWrapper />);

      expect(screen.queryByText('This field is required')).not.toBeInTheDocument();
    });

    it('should associate helper text with input via aria-describedby', () => {
      const error = { message: 'Error message' };
      renderWithProviders(
        <TestWrapper 
          name="testField"
          error={error} 
          helperText="Error message" 
        />
      );

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveAttribute('aria-describedby', 'testField-helper-text');
      expect(screen.getByText('Error message')).toHaveAttribute('id', 'testField-helper-text');
    });
  });

  describe('User Interaction', () => {
    it('should handle user input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      await user.type(input, 'test input');

      expect(input).toHaveValue('test input');
    });

    it('should handle clearing input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      await user.type(input, 'test input');
      expect(input).toHaveValue('test input');

      await user.clear(input);
      expect(input).toHaveValue('');
    });

    it('should handle focus and blur events', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      
      await user.click(input);
      expect(input).toHaveFocus();

      await user.tab();
      expect(input).not.toHaveFocus();
    });
  });

  describe('Props and Configuration', () => {
    it('should apply size prop correctly', () => {
      renderWithProviders(<TestWrapper size="medium" />);

      const input = screen.getByLabelText('Test Label');
      // Check if the input has the correct size attribute
      expect(input.closest('.MuiOutlinedInput-root')).toHaveClass('MuiOutlinedInput-sizeMedium');
    });

    it('should apply fullWidth prop correctly', () => {
      renderWithProviders(<TestWrapper fullWidth={false} />);

      const formControl = screen.getByLabelText('Test Label').closest('.MuiFormControl-root');
      expect(formControl).not.toHaveClass('MuiFormControl-fullWidth');
    });

    it('should apply margin prop correctly', () => {
      renderWithProviders(<TestWrapper margin="dense" />);

      const formControl = screen.getByLabelText('Test Label').closest('.MuiFormControl-root');
      expect(formControl).toHaveClass('MuiFormControl-marginDense');
    });

    it('should pass through additional props', () => {
      renderWithProviders(<TestWrapper data-testid="custom-input" />);

      expect(screen.getByTestId('custom-input')).toBeInTheDocument();
    });

    it('should handle disabled state', () => {
      renderWithProviders(<TestWrapper disabled />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toBeDisabled();
    });

    it('should handle required state', () => {
      renderWithProviders(<TestWrapper required />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toBeRequired();
    });
  });

  describe('Default Values', () => {
    it('should use default size when not specified', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      expect(input.closest('.MuiOutlinedInput-root')).toHaveClass('MuiOutlinedInput-sizeSmall');
    });

    it('should use default type when not specified', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveAttribute('type', 'text');
    });

    it('should be fullWidth by default', () => {
      renderWithProviders(<TestWrapper />);

      const formControl = screen.getByLabelText('Test Label').closest('.MuiFormControl-root');
      expect(formControl).toHaveClass('MuiFormControl-fullWidth');
    });

    it('should use normal margin by default', () => {
      renderWithProviders(<TestWrapper />);

      const formControl = screen.getByLabelText('Test Label').closest('.MuiFormControl-root');
      expect(formControl).toHaveClass('MuiFormControl-marginNormal');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty value gracefully', () => {
      renderWithProviders(<TestWrapper />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveValue('');
    });

    it('should handle null value gracefully', () => {
      const TestWrapperWithNullValue = () => {
        const { control } = useForm({
          defaultValues: { testField: null },
          mode: 'onChange'
        });

        return (
          <FormControlWrapper
            name="testField"
            control={control}
            label="Test Label"
          />
        );
      };

      renderWithProviders(<TestWrapperWithNullValue />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveValue('');
    });

    it('should handle undefined value gracefully', () => {
      const TestWrapperWithUndefinedValue = () => {
        const { control } = useForm({
          defaultValues: { testField: undefined },
          mode: 'onChange'
        });

        return (
          <FormControlWrapper
            name="testField"
            control={control}
            label="Test Label"
          />
        );
      };

      renderWithProviders(<TestWrapperWithUndefinedValue />);

      const input = screen.getByLabelText('Test Label');
      expect(input).toHaveValue('');
    });

    it('should handle very long input values', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const longText = 'a'.repeat(1000);
      const input = screen.getByLabelText('Test Label');
      
      await user.type(input, longText);
      expect(input).toHaveValue(longText);
    });

    it('should handle special characters in input', async () => {
      const user = userEvent.setup();
      renderWithProviders(<TestWrapper />);

      const specialText = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      const input = screen.getByLabelText('Test Label');
      
      await user.type(input, specialText);
      expect(input).toHaveValue(specialText);
    });
  });
});
