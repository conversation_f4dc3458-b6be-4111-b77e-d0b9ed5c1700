{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepConnectorUtilityClass(slot) {\n  return generateUtilityClass('MuiStepConnector', slot);\n}\nconst stepConnectorClasses = generateUtilityClasses('MuiStepConnector', ['root', 'horizontal', 'vertical', 'alternativeLabel', 'active', 'completed', 'disabled', 'line', 'lineHorizontal', 'lineVertical']);\nexport default stepConnectorClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getStepConnectorUtilityClass", "slot", "stepConnectorClasses"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/StepConnector/stepConnectorClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepConnectorUtilityClass(slot) {\n  return generateUtilityClass('MuiStepConnector', slot);\n}\nconst stepConnectorClasses = generateUtilityClasses('MuiStepConnector', ['root', 'horizontal', 'vertical', 'alternativeLabel', 'active', 'completed', 'disabled', 'line', 'lineHorizontal', 'lineVertical']);\nexport default stepConnectorClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOF,oBAAoB,CAAC,kBAAkB,EAAEE,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGJ,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;AAC5M,eAAeI,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}