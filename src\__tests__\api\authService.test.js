import axios from 'axios';
import authService from '../../api/authService';
import { mockAuthResponses, mockUserData } from '../utils/testUtils';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

describe('authService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  describe('getAuthHeaders helper', () => {
    it('should return headers without Authorization when no token', () => {
      // This tests the internal getAuthHeaders function indirectly
      expect(localStorage.getItem('token')).toBeNull();
    });

    it('should include Authorization header when token exists', () => {
      localStorage.setItem('token', 'test-token');
      expect(localStorage.getItem('token')).toBe('test-token');
    });
  });

  describe('login', () => {
    const credentials = { email: '<EMAIL>', password: 'password123' };

    it('should login successfully and store token', async () => {
      const mockResponse = mockAuthResponses.loginSuccess;
      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const successCallback = jest.fn();
      const errorCallback = jest.fn();

      const result = await authService.login(credentials, errorCallback, successCallback);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:8080/api/v1/login',
        credentials,
        { headers: { 'Content-Type': 'application/json' } }
      );
      expect(localStorage.setItem).toHaveBeenCalledWith('token', 'mock-jwt-token');
      expect(successCallback).toHaveBeenCalledWith(mockResponse.data);
      expect(errorCallback).not.toHaveBeenCalled();
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle login error with error callback', async () => {
      const mockError = mockAuthResponses.error;
      mockedAxios.post.mockRejectedValueOnce(mockError);

      const successCallback = jest.fn();
      const errorCallback = jest.fn();

      await expect(authService.login(credentials, errorCallback, successCallback)).rejects.toBe('Authentication failed');

      expect(errorCallback).toHaveBeenCalledWith('Authentication failed');
      expect(successCallback).not.toHaveBeenCalled();
      expect(localStorage.setItem).not.toHaveBeenCalled();
    });

    it('should handle network error', async () => {
      const networkError = mockAuthResponses.networkError;
      mockedAxios.post.mockRejectedValueOnce(networkError);

      const errorCallback = jest.fn();

      await expect(authService.login(credentials, errorCallback)).rejects.toBe('Network Error');

      expect(errorCallback).toHaveBeenCalledWith('Network Error');
    });

    it('should work without callbacks', async () => {
      const mockResponse = mockAuthResponses.loginSuccess;
      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const result = await authService.login(credentials);

      expect(result).toEqual(mockResponse.data);
      expect(localStorage.setItem).toHaveBeenCalledWith('token', 'mock-jwt-token');
    });
  });

  describe('signup', () => {
    const userData = {
      name: 'John Doe',
      email: '<EMAIL>',
      password: 'password123'
    };

    it('should signup successfully', async () => {
      const mockResponse = mockAuthResponses.signupSuccess;
      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const successCallback = jest.fn();
      const errorCallback = jest.fn();

      const result = await authService.signup(userData, errorCallback, successCallback);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:8080/api/v1/signup',
        userData,
        { headers: { 'Content-Type': 'application/json' } }
      );
      expect(successCallback).toHaveBeenCalledWith(mockResponse.data);
      expect(errorCallback).not.toHaveBeenCalled();
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle signup error', async () => {
      const mockError = { response: { data: { message: 'Email already exists' } } };
      mockedAxios.post.mockRejectedValueOnce(mockError);

      const errorCallback = jest.fn();

      await expect(authService.signup(userData, errorCallback)).rejects.toBe('Email already exists');

      expect(errorCallback).toHaveBeenCalledWith('Email already exists');
    });
  });

  describe('googleLogin', () => {
    const googleData = { code: 'google-auth-code' };

    it('should handle Google login successfully', async () => {
      const mockResponse = {
        data: {
          token: 'google-jwt-token',
          user: mockUserData
        }
      };
      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const successCallback = jest.fn();
      const result = await authService.googleLogin(googleData, null, successCallback);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:8080/api/v1/auth/google',
        googleData,
        { headers: { 'Content-Type': 'application/json' } }
      );
      expect(localStorage.setItem).toHaveBeenCalledWith('token', 'google-jwt-token');
      expect(localStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockUserData));
      expect(successCallback).toHaveBeenCalledWith(mockResponse.data);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle Google login error', async () => {
      const mockError = { response: { data: { message: 'Google authentication failed' } } };
      mockedAxios.post.mockRejectedValueOnce(mockError);

      const errorCallback = jest.fn();

      await expect(authService.googleLogin(googleData, errorCallback)).rejects.toBe('Google authentication failed');

      expect(errorCallback).toHaveBeenCalledWith('Google authentication failed');
    });
  });

  describe('getProfile', () => {
    beforeEach(() => {
      localStorage.setItem('token', 'test-token');
    });

    it('should fetch profile successfully', async () => {
      const mockResponse = mockAuthResponses.profileSuccess;
      mockedAxios.get.mockResolvedValueOnce(mockResponse);

      const successCallback = jest.fn();
      const result = await authService.getProfile(null, successCallback);

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'http://localhost:8080/api/v1/profile',
        { headers: { 'Content-Type': 'application/json', Authorization: 'Bearer test-token' } }
      );
      expect(successCallback).toHaveBeenCalledWith(mockResponse.data);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle profile fetch error', async () => {
      const mockError = { response: { data: { message: 'Unauthorized' } } };
      mockedAxios.get.mockRejectedValueOnce(mockError);

      const errorCallback = jest.fn();

      await expect(authService.getProfile(errorCallback)).rejects.toBe('Unauthorized');

      expect(errorCallback).toHaveBeenCalledWith('Unauthorized');
    });
  });

  describe('forgotPassword', () => {
    const email = '<EMAIL>';

    it('should send forgot password request successfully', async () => {
      const mockResponse = mockAuthResponses.forgotPasswordSuccess;
      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const successCallback = jest.fn();
      const result = await authService.forgotPassword(email, null, successCallback);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:8080/api/v1/forgot-password',
        { email },
        { headers: { 'Content-Type': 'application/json' } }
      );
      expect(successCallback).toHaveBeenCalledWith(mockResponse.data);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle forgot password error', async () => {
      const mockError = { response: { data: { message: 'Email not found' } } };
      mockedAxios.post.mockRejectedValueOnce(mockError);

      const errorCallback = jest.fn();

      await expect(authService.forgotPassword(email, errorCallback)).rejects.toBe('Email not found');

      expect(errorCallback).toHaveBeenCalledWith('Email not found');
    });
  });

  describe('logout', () => {
    it('should clear localStorage on logout', () => {
      localStorage.setItem('token', 'test-token');
      localStorage.setItem('user', JSON.stringify(mockUserData));

      authService.logout();

      expect(localStorage.removeItem).toHaveBeenCalledWith('token');
      expect(localStorage.removeItem).toHaveBeenCalledWith('user');
    });
  });

  describe('isAuthenticated', () => {
    it('should return true when token exists', () => {
      localStorage.setItem('token', 'test-token');
      expect(authService.isAuthenticated()).toBe(true);
    });

    it('should return false when no token', () => {
      expect(authService.isAuthenticated()).toBe(false);
    });
  });

  describe('getStoredUser', () => {
    it('should return parsed user data when exists', () => {
      localStorage.setItem('user', JSON.stringify(mockUserData));
      const result = authService.getStoredUser();
      expect(result).toEqual(mockUserData);
    });

    it('should return null when no user data', () => {
      const result = authService.getStoredUser();
      expect(result).toBeNull();
    });

    it('should handle invalid JSON gracefully', () => {
      localStorage.setItem('user', 'invalid-json');
      expect(() => authService.getStoredUser()).toThrow();
    });
  });

  describe('handleOAuthCallback', () => {
    const params = { code: 'oauth-code', state: 'oauth-state' };

    it('should handle OAuth callback successfully', async () => {
      const mockResponse = {
        data: {
          token: 'oauth-jwt-token',
          user: mockUserData
        }
      };
      mockedAxios.post.mockResolvedValueOnce(mockResponse);

      const successCallback = jest.fn();
      const result = await authService.handleOAuthCallback(params, null, successCallback);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        'http://localhost:8080/api/v1/auth/oauth/callback',
        params,
        { headers: { 'Content-Type': 'application/json' } }
      );
      expect(localStorage.setItem).toHaveBeenCalledWith('token', 'oauth-jwt-token');
      expect(localStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockUserData));
      expect(successCallback).toHaveBeenCalledWith(mockResponse.data);
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle OAuth callback error', async () => {
      const mockError = { response: { data: { message: 'Invalid OAuth code' } } };
      mockedAxios.post.mockRejectedValueOnce(mockError);

      const errorCallback = jest.fn();

      await expect(authService.handleOAuthCallback(params, errorCallback)).rejects.toBe('Invalid OAuth code');

      expect(errorCallback).toHaveBeenCalledWith('Invalid OAuth code');
    });
  });
});
