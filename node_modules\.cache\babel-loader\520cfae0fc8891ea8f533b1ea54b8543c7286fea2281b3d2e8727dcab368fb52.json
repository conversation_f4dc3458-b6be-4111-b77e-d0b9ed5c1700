{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _span;\nconst _excluded = [\"children\", \"className\", \"component\", \"disabled\", \"error\", \"filled\", \"focused\", \"margin\", \"required\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport styled from '../styles/styled';\nimport capitalize from '../utils/capitalize';\nimport formHelperTextClasses, { getFormHelperTextUtilityClasses } from './formHelperTextClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    contained,\n    size,\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', error && 'error', size && \"size\".concat(capitalize(size)), contained && 'contained', focused && 'focused', filled && 'filled', required && 'required']\n  };\n  return composeClasses(slots, getFormHelperTextUtilityClasses, classes);\n};\nconst FormHelperTextRoot = styled('p', {\n  name: 'MuiFormHelperText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.size && styles[\"size\".concat(capitalize(ownerState.size))], ownerState.contained && styles.contained, ownerState.filled && styles.filled];\n  }\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    color: (theme.vars || theme).palette.text.secondary\n  }, theme.typography.caption, {\n    textAlign: 'left',\n    marginTop: 3,\n    marginRight: 0,\n    marginBottom: 0,\n    marginLeft: 0,\n    [\"&.\".concat(formHelperTextClasses.disabled)]: {\n      color: (theme.vars || theme).palette.text.disabled\n    },\n    [\"&.\".concat(formHelperTextClasses.error)]: {\n      color: (theme.vars || theme).palette.error.main\n    }\n  }, ownerState.size === 'small' && {\n    marginTop: 4\n  }, ownerState.contained && {\n    marginLeft: 14,\n    marginRight: 14\n  });\n});\nconst FormHelperText = /*#__PURE__*/React.forwardRef(function FormHelperText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormHelperText'\n  });\n  const {\n      children,\n      className,\n      component = 'p'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['variant', 'size', 'disabled', 'error', 'filled', 'focused', 'required']\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    contained: fcs.variant === 'filled' || fcs.variant === 'outlined',\n    variant: fcs.variant,\n    size: fcs.size,\n    disabled: fcs.disabled,\n    error: fcs.error,\n    filled: fcs.filled,\n    focused: fcs.focused,\n    required: fcs.required\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FormHelperTextRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: children === ' ' ?\n    // notranslate needed while Google Translate will not fix zero-width space issue\n    _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n      className: \"notranslate\",\n      children: \"\\u200B\"\n    })) : children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormHelperText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   *\n   * If `' '` is provided, the component reserves one line height for displaying a future message.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the helper text should be displayed in a disabled state.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, helper text should be displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the helper text should use filled classes key.\n   */\n  filled: PropTypes.bool,\n  /**\n   * If `true`, the helper text should use focused classes key.\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `dense`, will adjust vertical spacing. This is normally obtained via context from\n   * FormControl.\n   */\n  margin: PropTypes.oneOf(['dense']),\n  /**\n   * If `true`, the helper text should use required classes key.\n   */\n  required: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined', 'standard']), PropTypes.string])\n} : void 0;\nexport default FormHelperText;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_span", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "formControlState", "useFormControl", "styled", "capitalize", "formHelperTextClasses", "getFormHelperTextUtilityClasses", "useDefaultProps", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "contained", "size", "disabled", "error", "filled", "focused", "required", "slots", "root", "concat", "FormHelperTextRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "color", "vars", "palette", "text", "secondary", "typography", "caption", "textAlign", "marginTop", "marginRight", "marginBottom", "marginLeft", "main", "FormHelperText", "forwardRef", "inProps", "ref", "children", "className", "component", "other", "muiFormControl", "fcs", "states", "variant", "as", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "elementType", "bool", "margin", "oneOf", "sx", "oneOfType", "arrayOf", "func"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/FormHelperText/FormHelperText.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _span;\nconst _excluded = [\"children\", \"className\", \"component\", \"disabled\", \"error\", \"filled\", \"focused\", \"margin\", \"required\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport formControlState from '../FormControl/formControlState';\nimport useFormControl from '../FormControl/useFormControl';\nimport styled from '../styles/styled';\nimport capitalize from '../utils/capitalize';\nimport formHelperTextClasses, { getFormHelperTextUtilityClasses } from './formHelperTextClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    contained,\n    size,\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', error && 'error', size && `size${capitalize(size)}`, contained && 'contained', focused && 'focused', filled && 'filled', required && 'required']\n  };\n  return composeClasses(slots, getFormHelperTextUtilityClasses, classes);\n};\nconst FormHelperTextRoot = styled('p', {\n  name: 'MuiFormHelperText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.size && styles[`size${capitalize(ownerState.size)}`], ownerState.contained && styles.contained, ownerState.filled && styles.filled];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  color: (theme.vars || theme).palette.text.secondary\n}, theme.typography.caption, {\n  textAlign: 'left',\n  marginTop: 3,\n  marginRight: 0,\n  marginBottom: 0,\n  marginLeft: 0,\n  [`&.${formHelperTextClasses.disabled}`]: {\n    color: (theme.vars || theme).palette.text.disabled\n  },\n  [`&.${formHelperTextClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}, ownerState.size === 'small' && {\n  marginTop: 4\n}, ownerState.contained && {\n  marginLeft: 14,\n  marginRight: 14\n}));\nconst FormHelperText = /*#__PURE__*/React.forwardRef(function FormHelperText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormHelperText'\n  });\n  const {\n      children,\n      className,\n      component = 'p'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['variant', 'size', 'disabled', 'error', 'filled', 'focused', 'required']\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    contained: fcs.variant === 'filled' || fcs.variant === 'outlined',\n    variant: fcs.variant,\n    size: fcs.size,\n    disabled: fcs.disabled,\n    error: fcs.error,\n    filled: fcs.filled,\n    focused: fcs.focused,\n    required: fcs.required\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(FormHelperTextRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other, {\n    children: children === ' ' ? // notranslate needed while Google Translate will not fix zero-width space issue\n    _span || (_span = /*#__PURE__*/_jsx(\"span\", {\n      className: \"notranslate\",\n      children: \"\\u200B\"\n    })) : children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormHelperText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   *\n   * If `' '` is provided, the component reserves one line height for displaying a future message.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the helper text should be displayed in a disabled state.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, helper text should be displayed in an error state.\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the helper text should use filled classes key.\n   */\n  filled: PropTypes.bool,\n  /**\n   * If `true`, the helper text should use focused classes key.\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `dense`, will adjust vertical spacing. This is normally obtained via context from\n   * FormControl.\n   */\n  margin: PropTypes.oneOf(['dense']),\n  /**\n   * If `true`, the helper text should use required classes key.\n   */\n  required: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['filled', 'outlined', 'standard']), PropTypes.string])\n} : void 0;\nexport default FormHelperText;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,KAAK;AACT,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,CAAC;AACnI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,qBAAqB,IAAIC,+BAA+B,QAAQ,yBAAyB;AAChG,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,SAAS;IACTC,IAAI;IACJC,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,QAAQ,IAAI,UAAU,EAAEC,KAAK,IAAI,OAAO,EAAEF,IAAI,WAAAQ,MAAA,CAAWlB,UAAU,CAACU,IAAI,CAAC,CAAE,EAAED,SAAS,IAAI,WAAW,EAAEK,OAAO,IAAI,SAAS,EAAED,MAAM,IAAI,QAAQ,EAAEE,QAAQ,IAAI,UAAU;EACxL,CAAC;EACD,OAAOnB,cAAc,CAACoB,KAAK,EAAEd,+BAA+B,EAAEM,OAAO,CAAC;AACxE,CAAC;AACD,MAAMW,kBAAkB,GAAGpB,MAAM,CAAC,GAAG,EAAE;EACrCqB,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJjB;IACF,CAAC,GAAGgB,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,IAAI,EAAEV,UAAU,CAACG,IAAI,IAAIc,MAAM,QAAAN,MAAA,CAAQlB,UAAU,CAACO,UAAU,CAACG,IAAI,CAAC,EAAG,EAAEH,UAAU,CAACE,SAAS,IAAIe,MAAM,CAACf,SAAS,EAAEF,UAAU,CAACM,MAAM,IAAIW,MAAM,CAACX,MAAM,CAAC;EACrK;AACF,CAAC,CAAC,CAACY,IAAA;EAAA,IAAC;IACFC,KAAK;IACLnB;EACF,CAAC,GAAAkB,IAAA;EAAA,OAAKnC,QAAQ,CAAC;IACbqC,KAAK,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACC,IAAI,CAACC;EAC5C,CAAC,EAAEL,KAAK,CAACM,UAAU,CAACC,OAAO,EAAE;IAC3BC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE,CAAC;IACb,MAAApB,MAAA,CAAMjB,qBAAqB,CAACU,QAAQ,IAAK;MACvCgB,KAAK,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACC,IAAI,CAACnB;IAC5C,CAAC;IACD,MAAAO,MAAA,CAAMjB,qBAAqB,CAACW,KAAK,IAAK;MACpCe,KAAK,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACjB,KAAK,CAAC2B;IAC7C;EACF,CAAC,EAAEhC,UAAU,CAACG,IAAI,KAAK,OAAO,IAAI;IAChCyB,SAAS,EAAE;EACb,CAAC,EAAE5B,UAAU,CAACE,SAAS,IAAI;IACzB6B,UAAU,EAAE,EAAE;IACdF,WAAW,EAAE;EACf,CAAC,CAAC;AAAA,EAAC;AACH,MAAMI,cAAc,GAAG,aAAa/C,KAAK,CAACgD,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAMpB,KAAK,GAAGpB,eAAe,CAAC;IAC5BoB,KAAK,EAAEmB,OAAO;IACdtB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFwB,QAAQ;MACRC,SAAS;MACTC,SAAS,GAAG;IACd,CAAC,GAAGvB,KAAK;IACTwB,KAAK,GAAG1D,6BAA6B,CAACkC,KAAK,EAAE/B,SAAS,CAAC;EACzD,MAAMwD,cAAc,GAAGlD,cAAc,CAAC,CAAC;EACvC,MAAMmD,GAAG,GAAGpD,gBAAgB,CAAC;IAC3B0B,KAAK;IACLyB,cAAc;IACdE,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU;EAClF,CAAC,CAAC;EACF,MAAM3C,UAAU,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAEiC,KAAK,EAAE;IACrCuB,SAAS;IACTrC,SAAS,EAAEwC,GAAG,CAACE,OAAO,KAAK,QAAQ,IAAIF,GAAG,CAACE,OAAO,KAAK,UAAU;IACjEA,OAAO,EAAEF,GAAG,CAACE,OAAO;IACpBzC,IAAI,EAAEuC,GAAG,CAACvC,IAAI;IACdC,QAAQ,EAAEsC,GAAG,CAACtC,QAAQ;IACtBC,KAAK,EAAEqC,GAAG,CAACrC,KAAK;IAChBC,MAAM,EAAEoC,GAAG,CAACpC,MAAM;IAClBC,OAAO,EAAEmC,GAAG,CAACnC,OAAO;IACpBC,QAAQ,EAAEkC,GAAG,CAAClC;EAChB,CAAC,CAAC;EACF,MAAMP,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACc,kBAAkB,EAAE7B,QAAQ,CAAC;IACpD8D,EAAE,EAAEN,SAAS;IACbvC,UAAU,EAAEA,UAAU;IACtBsC,SAAS,EAAElD,IAAI,CAACa,OAAO,CAACS,IAAI,EAAE4B,SAAS,CAAC;IACxCF,GAAG,EAAEA;EACP,CAAC,EAAEI,KAAK,EAAE;IACRH,QAAQ,EAAEA,QAAQ,KAAK,GAAG;IAAG;IAC7BrD,KAAK,KAAKA,KAAK,GAAG,aAAac,IAAI,CAAC,MAAM,EAAE;MAC1CwC,SAAS,EAAE,aAAa;MACxBD,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC,GAAGA;EACR,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGf,cAAc,CAACgB,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;EACEZ,QAAQ,EAAElD,SAAS,CAAC+D,IAAI;EACxB;AACF;AACA;EACEjD,OAAO,EAAEd,SAAS,CAACgE,MAAM;EACzB;AACF;AACA;EACEb,SAAS,EAAEnD,SAAS,CAACiE,MAAM;EAC3B;AACF;AACA;AACA;EACEb,SAAS,EAAEpD,SAAS,CAACkE,WAAW;EAChC;AACF;AACA;EACEjD,QAAQ,EAAEjB,SAAS,CAACmE,IAAI;EACxB;AACF;AACA;EACEjD,KAAK,EAAElB,SAAS,CAACmE,IAAI;EACrB;AACF;AACA;EACEhD,MAAM,EAAEnB,SAAS,CAACmE,IAAI;EACtB;AACF;AACA;EACE/C,OAAO,EAAEpB,SAAS,CAACmE,IAAI;EACvB;AACF;AACA;AACA;EACEC,MAAM,EAAEpE,SAAS,CAACqE,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;EAClC;AACF;AACA;EACEhD,QAAQ,EAAErB,SAAS,CAACmE,IAAI;EACxB;AACF;AACA;EACEG,EAAE,EAAEtE,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACwE,OAAO,CAACxE,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAACgE,MAAM,EAAEhE,SAAS,CAACmE,IAAI,CAAC,CAAC,CAAC,EAAEnE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAACgE,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEP,OAAO,EAAEzD,SAAS,CAAC,sCAAsCuE,SAAS,CAAC,CAACvE,SAAS,CAACqE,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,EAAErE,SAAS,CAACiE,MAAM,CAAC;AAC5I,CAAC,GAAG,KAAK,CAAC;AACV,eAAenB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}