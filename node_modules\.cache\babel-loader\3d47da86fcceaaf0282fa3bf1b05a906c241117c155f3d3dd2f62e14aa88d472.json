{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"focusVisibleClassName\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport cardActionAreaClasses, { getCardActionAreaUtilityClass } from './cardActionAreaClasses';\nimport ButtonBase from '../ButtonBase';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    focusHighlight: ['focusHighlight']\n  };\n  return composeClasses(slots, getCardActionAreaUtilityClass, classes);\n};\nconst CardActionAreaRoot = styled(ButtonBase, {\n  name: 'MuiCardActionArea',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    display: 'block',\n    textAlign: 'inherit',\n    borderRadius: 'inherit',\n    // for Safari to work https://github.com/mui/material-ui/issues/36285.\n    width: '100%',\n    [\"&:hover .\".concat(cardActionAreaClasses.focusHighlight)]: {\n      opacity: (theme.vars || theme).palette.action.hoverOpacity,\n      '@media (hover: none)': {\n        opacity: 0\n      }\n    },\n    [\"&.\".concat(cardActionAreaClasses.focusVisible, \" .\").concat(cardActionAreaClasses.focusHighlight)]: {\n      opacity: (theme.vars || theme).palette.action.focusOpacity\n    }\n  };\n});\nconst CardActionAreaFocusHighlight = styled('span', {\n  name: 'MuiCardActionArea',\n  slot: 'FocusHighlight',\n  overridesResolver: (props, styles) => styles.focusHighlight\n})(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    overflow: 'hidden',\n    pointerEvents: 'none',\n    position: 'absolute',\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    borderRadius: 'inherit',\n    opacity: 0,\n    backgroundColor: 'currentcolor',\n    transition: theme.transitions.create('opacity', {\n      duration: theme.transitions.duration.short\n    })\n  };\n});\nconst CardActionArea = /*#__PURE__*/React.forwardRef(function CardActionArea(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActionArea'\n  });\n  const {\n      children,\n      className,\n      focusVisibleClassName\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(CardActionAreaRoot, _extends({\n    className: clsx(classes.root, className),\n    focusVisibleClassName: clsx(focusVisibleClassName, classes.focusVisible),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [children, /*#__PURE__*/_jsx(CardActionAreaFocusHighlight, {\n      className: classes.focusHighlight,\n      ownerState: ownerState\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActionArea.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActionArea;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "clsx", "composeClasses", "useDefaultProps", "styled", "cardActionAreaClasses", "getCardActionAreaUtilityClass", "ButtonBase", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "focusHighlight", "CardActionAreaRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "display", "textAlign", "borderRadius", "width", "concat", "opacity", "vars", "palette", "action", "hoverOpacity", "focusVisible", "focusOpacity", "CardActionAreaFocusHighlight", "_ref2", "overflow", "pointerEvents", "position", "top", "right", "bottom", "left", "backgroundColor", "transition", "transitions", "create", "duration", "short", "CardActionArea", "forwardRef", "inProps", "ref", "children", "className", "focusVisibleClassName", "other", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/Documents/Project/sample-authentication/node_modules/@mui/material/CardActionArea/CardActionArea.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"focusVisibleClassName\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport cardActionAreaClasses, { getCardActionAreaUtilityClass } from './cardActionAreaClasses';\nimport ButtonBase from '../ButtonBase';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    focusHighlight: ['focusHighlight']\n  };\n  return composeClasses(slots, getCardActionAreaUtilityClass, classes);\n};\nconst CardActionAreaRoot = styled(ButtonBase, {\n  name: 'MuiCardActionArea',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  display: 'block',\n  textAlign: 'inherit',\n  borderRadius: 'inherit',\n  // for Safari to work https://github.com/mui/material-ui/issues/36285.\n  width: '100%',\n  [`&:hover .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.hoverOpacity,\n    '@media (hover: none)': {\n      opacity: 0\n    }\n  },\n  [`&.${cardActionAreaClasses.focusVisible} .${cardActionAreaClasses.focusHighlight}`]: {\n    opacity: (theme.vars || theme).palette.action.focusOpacity\n  }\n}));\nconst CardActionAreaFocusHighlight = styled('span', {\n  name: 'MuiCardActionArea',\n  slot: 'FocusHighlight',\n  overridesResolver: (props, styles) => styles.focusHighlight\n})(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  pointerEvents: 'none',\n  position: 'absolute',\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0,\n  borderRadius: 'inherit',\n  opacity: 0,\n  backgroundColor: 'currentcolor',\n  transition: theme.transitions.create('opacity', {\n    duration: theme.transitions.duration.short\n  })\n}));\nconst CardActionArea = /*#__PURE__*/React.forwardRef(function CardActionArea(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardActionArea'\n  });\n  const {\n      children,\n      className,\n      focusVisibleClassName\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(CardActionAreaRoot, _extends({\n    className: clsx(classes.root, className),\n    focusVisibleClassName: clsx(focusVisibleClassName, classes.focusVisible),\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: [children, /*#__PURE__*/_jsx(CardActionAreaFocusHighlight, {\n      className: classes.focusHighlight,\n      ownerState: ownerState\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardActionArea.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  focusVisibleClassName: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardActionArea;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,uBAAuB,CAAC;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,qBAAqB,IAAIC,6BAA6B,QAAQ,yBAAyB;AAC9F,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,cAAc,EAAE,CAAC,gBAAgB;EACnC,CAAC;EACD,OAAOf,cAAc,CAACa,KAAK,EAAET,6BAA6B,EAAEQ,OAAO,CAAC;AACtE,CAAC;AACD,MAAMI,kBAAkB,GAAGd,MAAM,CAACG,UAAU,EAAE;EAC5CY,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAACQ,IAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,OAAO,EAAE,OAAO;IAChBC,SAAS,EAAE,SAAS;IACpBC,YAAY,EAAE,SAAS;IACvB;IACAC,KAAK,EAAE,MAAM;IACb,aAAAC,MAAA,CAAazB,qBAAqB,CAACY,cAAc,IAAK;MACpDc,OAAO,EAAE,CAACN,KAAK,CAACO,IAAI,IAAIP,KAAK,EAAEQ,OAAO,CAACC,MAAM,CAACC,YAAY;MAC1D,sBAAsB,EAAE;QACtBJ,OAAO,EAAE;MACX;IACF,CAAC;IACD,MAAAD,MAAA,CAAMzB,qBAAqB,CAAC+B,YAAY,QAAAN,MAAA,CAAKzB,qBAAqB,CAACY,cAAc,IAAK;MACpFc,OAAO,EAAE,CAACN,KAAK,CAACO,IAAI,IAAIP,KAAK,EAAEQ,OAAO,CAACC,MAAM,CAACG;IAChD;EACF,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,4BAA4B,GAAGlC,MAAM,CAAC,MAAM,EAAE;EAClDe,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACsB,KAAA;EAAA,IAAC;IACFd;EACF,CAAC,GAAAc,KAAA;EAAA,OAAM;IACLC,QAAQ,EAAE,QAAQ;IAClBC,aAAa,EAAE,MAAM;IACrBC,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;IACPlB,YAAY,EAAE,SAAS;IACvBG,OAAO,EAAE,CAAC;IACVgB,eAAe,EAAE,cAAc;IAC/BC,UAAU,EAAEvB,KAAK,CAACwB,WAAW,CAACC,MAAM,CAAC,SAAS,EAAE;MAC9CC,QAAQ,EAAE1B,KAAK,CAACwB,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC;EACH,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,cAAc,GAAG,aAAatD,KAAK,CAACuD,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAMlC,KAAK,GAAGnB,eAAe,CAAC;IAC5BmB,KAAK,EAAEiC,OAAO;IACdpC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFsC,QAAQ;MACRC,SAAS;MACTC;IACF,CAAC,GAAGrC,KAAK;IACTsC,KAAK,GAAG/D,6BAA6B,CAACyB,KAAK,EAAExB,SAAS,CAAC;EACzD,MAAMe,UAAU,GAAGS,KAAK;EACxB,MAAMR,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,KAAK,CAACO,kBAAkB,EAAEtB,QAAQ,CAAC;IACrD8D,SAAS,EAAEzD,IAAI,CAACa,OAAO,CAACE,IAAI,EAAE0C,SAAS,CAAC;IACxCC,qBAAqB,EAAE1D,IAAI,CAAC0D,qBAAqB,EAAE7C,OAAO,CAACsB,YAAY,CAAC;IACxEoB,GAAG,EAAEA,GAAG;IACR3C,UAAU,EAAEA;EACd,CAAC,EAAE+C,KAAK,EAAE;IACRH,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAahD,IAAI,CAAC6B,4BAA4B,EAAE;MACnEoB,SAAS,EAAE5C,OAAO,CAACG,cAAc;MACjCJ,UAAU,EAAEA;IACd,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFgD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,cAAc,CAACW,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;EACEP,QAAQ,EAAEzD,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;EACEnD,OAAO,EAAEd,SAAS,CAACkE,MAAM;EACzB;AACF;AACA;EACER,SAAS,EAAE1D,SAAS,CAACmE,MAAM;EAC3B;AACF;AACA;EACER,qBAAqB,EAAE3D,SAAS,CAACmE,MAAM;EACvC;AACF;AACA;EACEC,EAAE,EAAEpE,SAAS,CAACqE,SAAS,CAAC,CAACrE,SAAS,CAACsE,OAAO,CAACtE,SAAS,CAACqE,SAAS,CAAC,CAACrE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACkE,MAAM,EAAElE,SAAS,CAACwE,IAAI,CAAC,CAAC,CAAC,EAAExE,SAAS,CAACuE,IAAI,EAAEvE,SAAS,CAACkE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}